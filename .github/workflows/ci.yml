name: Build, Test, and Deploy

on:
  push:
    branches:
      - "main"
      - "feature/v*"
    tags:
      - "v*"
  pull_request:
    branches:
      - 'feature/v*'  # feature/V で始まるブランチの PR に対してのみトリガーされる

env:
  AWS_REGION: "ap-northeast-1"
  IMAGE_TAG: "latest"

jobs:
  build-deploy:
    runs-on: self-hosted
    permissions:
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'corretto'

      - name: Verify Java version
        run: |
          echo "--- Java Version ---"
          java -version
          echo "--- Maven Version ---"
          mvn --version

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Build with Maven
        run: mvn clean package

      - name: Determine AWS Account
        if: github.event_name == 'push'
        id: aws-account
        run: |
          if [[ $GITHUB_REF == refs/heads/feature/* ]]; then
            echo "ACCOUNT_ID=************" >> $GITHUB_OUTPUT
            echo "ECR_REPO_NAME=ms-bp-batch-ecr-dev-standard" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == refs/heads/main ]]; then
            echo "ACCOUNT_ID=************" >> $GITHUB_OUTPUT
            echo "ECR_REPO_NAME=ms-bp-batch-ecr-stg-standard" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == refs/tags/* ]]; then
            echo "ACCOUNT_ID=************" >> $GITHUB_OUTPUT
            echo "ECR_REPO_NAME=ms-bp-batch-ecr-prod-standard" >> $GITHUB_OUTPUT
          fi

      - name: Assume Role
        if: github.event_name == 'push'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ steps.aws-account.outputs.ACCOUNT_ID }}:role/GitHubRunnerCrossAccountRole
          aws-region: ap-northeast-1
          role-session-name: GitHubActionsSession

      - name: Set ecr path
        if: github.event_name == 'push'
        id: ecr-path
        run: |
          echo "ECR_REPOSITORY_URI=${{ steps.aws-account.outputs.ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ steps.aws-account.outputs.ECR_REPO_NAME }}" >> $GITHUB_OUTPUT

      - name: Login ecr
        if: github.event_name == 'push'
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }} | docker login --username AWS --password-stdin ${{ steps.ecr-path.outputs.ECR_REPOSITORY_URI }}

      - name: Build and Push Docker Image
        if: github.event_name == 'push'
        run: |
          docker build -t ${{ steps.ecr-path.outputs.ECR_REPOSITORY_URI }}:${{ env.IMAGE_TAG }} \
            --build-arg JAR_FILE=target/business-plan-batch*.jar \
            -f Dockerfile .
          docker push ${{ steps.ecr-path.outputs.ECR_REPOSITORY_URI }}:${{ env.IMAGE_TAG }}
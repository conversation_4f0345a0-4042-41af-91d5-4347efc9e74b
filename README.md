# バッチ処理システム

AWS S3とPostgreSQLデータベース間でのデータインポート・エクスポート処理を統合的に実装したバッチ処理です。

## プロジェクト概要

AWS環境でのデータ連携を効率的に行うためのバッチ処理アプリケーションです。

- **共通マスタ取得バッチ**: S3からPostgreSQLへのマスタデータインポート
- **事業計画（直接）連携バッチ**: PostgreSQLからS3への事業計画データエクスポート

## アーキテクチャ概要

### 統一エントリーポイント設計
- `BatchApplication`が全てのバッチ処理の起点となる
- 起動パラメーター`jobId`に基づいて適切なアプリケーションに処理を委譲
- 構造化ログとMDCによる実行コンテキストの管理

### 主要コンポーネント
```
BatchApplication (統一エントリーポイント)
├── CommonMasterImportRunner (共通マスタ取得バッチ)
└── BusinessPlanExportRunner (事業計画連携バッチ)
```

## 主要機能

### 共通マスタ取得バッチ (CommonMasterImportRunner)
**業務概要**: AWS RDS PostgreSQLのS3インポート機能を使用してS3から直接データベースにマスタデータをインポート
**設定ファイル**: `common-master-import.yml`

### 事業計画（直接）連携バッチ (BusinessPlanExportRunner)
**業務概要**: データベースから事業計画データを抽出してS3にエクスポート
**設定ファイル**: `business-plan-export.yml`

## 技術仕様

### 開発環境
- **Java**: 21 (LTS)
- **ビルドツール**: Maven 3.6+
- **AWS SDK**: 2.31.50
- **データベース**: PostgreSQL (Aurora) with aws_s3 extension
- **接続プール**: HikariCP 5.0.1
- **ログ**: SLF4J + Logback
- **テスト**: JUnit 5 + Mockito

### 主要依存関係
```xml
<dependencies>
    <!-- AWS SDK -->
    <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3</artifactId>
        <version>2.31.50</version>
    </dependency>

    <!-- データベース -->
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.7.2</version>
    </dependency>

    <!-- 接続プール -->
    <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
        <version>5.0.1</version>
    </dependency>
</dependencies>
```

## 設定方法

### アプリケーション設定 (application.properties)
```properties
# アプリケーション基本設定
app.name=batch-import-system
app.version=1.0.0

# アクティブプロファイル
spring.profiles.active=dev

# 設定ファイルパス
common.master.import.config.path=common-master-import.yml
business.plan.export.config.path=business-plan-export.yml

# S3インポート設定
s3.import.aws.region=ap-northeast-1

# データベース設定
db.connection.timeout=5
db.parameter.prefix=/ms-bp/dev/standard/db

# CSV処理設定
csv.encoding=UTF-8

# ログ設定
logging.level.root=INFO
logging.level.org.ms.bp=DEBUG
```

### 環境別設定ファイル
- `application.properties`: デフォルト（開発環境）
- `application-uat.properties`: UAT環境用設定
- `application-prod.properties`: 本番環境用設定

### YAML設定ファイル

#### common-master-import.yml
```yaml
# 共通マスタ取得バッチ設定ファイル（S3インポート版）
aws:
  region: "ap-northeast-1"

jobs:
  - taskId: "import-user-master"
    source:
      bucketName: "common-master-bucket"
      masterPhysicalName: "user_master_"
      defaultFilePath: "master-data/"
      supportsDynamicPath: true
    target:
      tableName: "m_user"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: true
      encoding: "UTF-8"
    columnList: "user_id,user_name,email,department_code,position_code,active_flag,created_at,updated_at"
```

#### business-plan-export.yml
```yaml
# 事業計画（直接）連携バッチ設定ファイル
jobs:
  - taskId: "export-business-plan-summary"
    source:
      query: |
        SELECT
          bp.plan_year,
          bp.department_code,
          d.department_name,
          bp.project_code,
          p.project_name,
          bp.budget_amount,
          bp.actual_amount,
          bp.variance_amount,
          bp.variance_rate,
          bp.status,
          bp.created_at,
          bp.updated_at
        FROM business_plan bp
        LEFT JOIN m_department d ON bp.department_code = d.department_code
        LEFT JOIN m_project p ON bp.project_code = p.project_code
        WHERE bp.plan_year = ?
          AND bp.status IN ('APPROVED', 'EXECUTING')
        ORDER BY bp.department_code, bp.project_code
      parameters:
        plan_year: "2024"
    target:
      bucketName: "business-plan-export-bucket"
      filePath: "business-plan/summary/business_plan_summary_${date}.csv"
      includeHeader: true
      encoding: "UTF-8"
      delimiter: ","
```

**注意**: フィールド名のマッピング（日本語列名など）が必要な場合は、SQLクエリ内でAS別名を使用してください：

```yaml
source:
  query: |
    SELECT
      bp.plan_year AS "計画年度",
      bp.department_code AS "部門コード",
      bp.budget_amount AS "予算金額"
    FROM business_plan bp
    WHERE bp.plan_year = ?
```

## 実行方法

### 起動パラメーター

| パラメーター | 必須 | 説明 | 例 |
|-------------|------|------|-----|
| `jobId` | ✅ | バッチジョブID（業務タイプ判定用） | `jobId=COMMON_MASTER_IMPORT` |
| `targetDate` | ❌ | 対象年月日（YYYYMMDD形式またはUTC時間戳） | `targetDate=20240624` |
| `s3-path` | ❌ | CSVファイルのS3パス | `s3-path=master-data/user_master_20240624.csv` |
| `bucket-name` | ❌ | S3バケット名 | `bucket-name=custom-bucket` |

### 実行例

#### 共通マスタ取得バッチの実行
```bash
# 基本実行（前日データを自動取得）
java -jar target/business-plan-batch-1.0-SNAPSHOT-shaded.jar jobId=COMMON_MASTER_IMPORT

# 特定日付のデータを取得
java -jar target/business-plan-batch-1.0-SNAPSHOT-shaded.jar jobId=COMMON_MASTER_IMPORT targetDate=20240624

# カスタムS3パスを指定
java -jar target/business-plan-batch-1.0-SNAPSHOT-shaded.jar jobId=COMMON_MASTER_IMPORT s3-path=master-data/user_master_20240624.csv
```

#### 事業計画（直接）連携バッチの実行
```bash
# 基本実行（当日データをエクスポート）
java -jar target/business-plan-batch-1.0-SNAPSHOT-shaded.jar jobId=BUSINESS_PLAN_EXPORT

# 特定日付でエクスポート
java -jar target/business-plan-batch-1.0-SNAPSHOT-shaded.jar jobId=BUSINESS_PLAN_EXPORT targetDate=20240624

# カスタムS3パスを指定
java -jar target/business-plan-batch-1.0-SNAPSHOT-shaded.jar jobId=BUSINESS_PLAN_EXPORT s3-path=exports/business-plan/custom_export_20240624.csv
```

## 開発環境セットアップ

### 環境要件

- **Java 21+**
- **Maven 3.6+**
- **AWSアカウント**と適切な権限（S3、Parameter Store、Secrets Manager）
- **PostgreSQL with aws_s3 extension** (S3インポート機能用)

### プロジェクトビルド

```bash
# プロジェクトをクローン
git clone <repository-url>
cd batch

# 依存関係をインストールしてビルド
mvn clean compile

# 実行可能JARファイルを作成
mvn package
```

### AWS認証情報の設定

```bash
# AWS CLIの設定
aws configure

# または環境変数で設定
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key
export AWS_DEFAULT_REGION=ap-northeast-1
```

### データベース接続設定

- AWS Parameter StoreまたはSecrets Managerに接続情報を設定
- `application.properties`で接続パラメータのプレフィックスを確認
- データベース接続パラメータ例：
  - `/ms-bp/dev/standard/db/host`
  - `/ms-bp/dev/standard/db/port`
  - `/ms-bp/dev/standard/db/database`
  - `/ms-bp/dev/standard/db/username`
  - `/ms-bp/dev/standard/db/password`

### IDE設定

#### IntelliJ IDEA
1. プロジェクトをインポート（Maven project）
2. Project SDK: Java 21
3. Language level: 21
4. Annotation processing: 有効化
package org.ms.bp.runner;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class BusinessPlanIndirectExportRunnerTest {

    @BeforeEach
    void setUp() {
    }
    @Test
    void run() {

        resultCode1();
    }

    @Test
    void resultCode0(){
        String jobId="BUSINESS_PLAN_INDIRECT_EXPORT";
        String NENDO="2026";

        String parms[] = new String[2];
        parms[0]=jobId;
        parms[1]=NENDO;

        BusinessPlanIndirectExportRunner exportRunner = new BusinessPlanIndirectExportRunner(parms);

        //assert(0==exportRunner.run());
    }

    @Test
    void resultCode1(){
        String jobId="BUSINESS_PLAN_INDIRECT_EXPORT";
        String NENDO="20265";

        String parms[] = new String[2];
        parms[0]=jobId;
        parms[1]=NENDO;

        BusinessPlanIndirectExportRunner exportRunner = new BusinessPlanIndirectExportRunner(parms);

        //assert(1==exportRunner.run());
    }


}
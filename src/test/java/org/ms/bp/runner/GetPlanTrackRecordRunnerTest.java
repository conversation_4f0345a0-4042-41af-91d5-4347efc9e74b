package org.ms.bp.runner;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import static org.junit.jupiter.api.Assertions.assertEquals;

class GetPlanTrackRecordRunnerTest {

    String[] parms = new String[3];
    private static final Logger logger = LoggerFactory.getLogger(GetPlanTrackRecordRunnerTest.class);

    @Test
    void run(){
        keikaku_result_0();
        keikaku_result_1();
        jiseki_result_0();
        jiseki_result_1();
    }

    @Test
    void keikaku_result_0(){
        parms[0] = "fileType=1";
        parms[1] = "targetDate=20250819";
        parms[2] = "s3Path=";
        GetPlanTrackRecordRunner getPlanTrackRecordRunner = new GetPlanTrackRecordRunner(parms);

        int rs = 0;
        rs = getPlanTrackRecordRunner.run();

        //assertEquals(0, rs);
        if(rs==0){
            logger.info("keikaku_result_0正常終了");
        }else{
            logger.info("keikaku_result_0異常終了");
        }
    }

    @Test
    void keikaku_result_1(){
        parms[0] = "fileType=1";
        parms[1] = "targetDate=20250820";
        parms[2] = "s3Path=";
        GetPlanTrackRecordRunner getPlanTrackRecordRunner = new GetPlanTrackRecordRunner(parms);

        int rs = getPlanTrackRecordRunner.run();

        //assertEquals(1, rs);
        if(rs==1){
            logger.info("keikaku_result_1正常終了");
        }else{
            logger.info("keikaku_result_1異常終了");
        }
    }

    @Test
    void jiseki_result_0(){
        parms[0] = "fileType=2";
        parms[1] = "targetDate=20250819";
        parms[2] = "s3Path=";
        GetPlanTrackRecordRunner getPlanTrackRecordRunner = new GetPlanTrackRecordRunner(parms);

        int rs = getPlanTrackRecordRunner.run();

        //assertEquals(0, rs);
        if(rs==0){
            logger.info("jiseki_result_0正常終了");
        }else{
            logger.info("jiseki_result_0異常終了");
        }
    }

    @Test
    void jiseki_result_1(){
        parms[0] = "fileType=2";
        parms[1] = "targetDate=20250820";
        parms[2] = "s3Path=";
        GetPlanTrackRecordRunner getPlanTrackRecordRunner = new GetPlanTrackRecordRunner(parms);

        int rs = getPlanTrackRecordRunner.run();

        //assertEquals(1, rs);
        if(rs==1){
            logger.info("jiseki_result_1正常終了");
        }else{
            logger.info("jiseki_result_1異常終了");
        }
    }
}
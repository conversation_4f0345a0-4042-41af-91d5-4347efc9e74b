package org.ms.bp.runner;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

class CommonMasterImportRunnerTest {

    String[] parms = new String[0];
    private static final Logger logger = LoggerFactory.getLogger(CommonMasterImportRunnerTest.class);


    @BeforeEach
    void setUp(){

        setParams();
    }

    @Test
    void run() {
        resultCode0();
        resultCode1();
    }

    @Test
    void resultCode0(){
        /*
        String jobId="COMMON_MASTER_IMPORT";
        String targetDate="targetDate=20250806";
        String s3Path = "s3Path=in/TO_JIGYOKEIKAKU/KYOTSU_MST";

        String parms[] = new String[2];
        parms[0]=targetDate;
        parms[1]=s3Path;
         */

        CommonMasterImportRunner exportRunner = new CommonMasterImportRunner(parms);

        int rs = 0;
        rs = exportRunner.run();

        if(rs==0){
            logger.info("resultCode0正常終了");
        }else{
            logger.info("resultCode0異常終了");
        }
        //assert(rs==0);
        //(0, exportRunner.run());
    }

    @Test
    void resultCode1(){
        /*
        String jobId="COMMON_MASTER_IMPORT";
        String targetDate="20250808";

        String parms[] = new String[2];
        parms[0]=jobId;
        parms[1]=targetDate;
         */

        parms[0]="targetDate=20250808";

        CommonMasterImportRunner exportRunner = new CommonMasterImportRunner(parms);

        int rs = 0;
        rs = exportRunner.run();

        if(rs==1){
            logger.info("resultCode1正常終了");
        }else{
            logger.info("resultCode1異常終了");
        }
        //assert(rs==1);
        //assert(1==exportRunner.run());
    }

    @Test
    void siteiTable(){

        parms[2]="tableName=M_JK_KENMUMASTER";
        CommonMasterImportRunner exportRunner = new CommonMasterImportRunner(parms);

        int rs = 0;
        rs = exportRunner.run();

        if(rs==0){
            logger.info("siteiTable正常終了");
        }else{
            logger.info("siteiTable異常終了");
        }

        //assertEquals(0, rs);
    }

    private void setParams(){

        String jobId="COMMON_MASTER_IMPORT";
        String targetDate="targetDate=20250806";
        String s3Path = "s3Path=in/TO_JIGYOKEIKAKU/KYOTSU_MST";

        parms = new String[3];
        parms[0]=targetDate;
        parms[1]=s3Path;
        parms[2]="";
    }
}
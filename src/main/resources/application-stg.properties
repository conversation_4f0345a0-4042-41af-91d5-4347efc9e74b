# STG Environment Configuration
# Application Basic Settings
app.name=batch-import-system
app.version=1.0.0

# Active Profile
spring.profiles.active=stg
db.parameter.prefix=/ms-bp/stg/standard/db

# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
# SSH Tunnel configuration for connecting to AWS dev database
ssh.tunnel.enabled=false

aws.s3.bucket.name=ms-bp-files-stg-standard

# Logging Settings for STG
logging.level.root=INFO
logging.level.org.ms.bp=DEBUG

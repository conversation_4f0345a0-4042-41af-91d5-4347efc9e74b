# 共通マスタ取得バッチ設定ファイル（S3インポート版）
# 業務名: 共通マスタ取得バッチ
# AWS RDS PostgreSQLのS3インポート機能を使用

aws:
  region: "ap-northeast-1"

jobs:
  - taskId: "BAT_014"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_SHAINMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_SHAINMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      SYSTM_UNYO_KIGYO_CODE,
      SHAIN_CODE,
      SHNM_KANA,
      SHNM_KANJI,
      UNIT_CODE,
      UNIT_CODE_2,
      SHAIN_SHNG_KUBUN,
      GYOMU_KUBUN,
      SHKSH_KUBUN,
      SHKK_KUBUN,
      YKSHK_KUBUN,
      MAIL_ADDRS,
      KEIHI_SSN_WF_KNYK<PERSON>_CODE,
      KHSSN_WF_KNYKK_SHTN_CODE,
      YOKIN_SHBTS_KUBUN,
      KEIHI_SSN_WF_KOZA_BANGO,
      KEIHI_SSN_WF_KZMG_KANJI,
      KEIHI_SSN_WF_KZMG_KANA,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ

  - taskId: "BAT_015"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_UNITMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_UNITMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      UNIT_CODE,
      KSHB,
      SHRYB,
      KYTS_UNIT_KUBUN,
      UNIT_BB_KUBUN,
      GROUP_CODE,
      UNIT_MEI_KANA,
      UNIT_MEI_KANJI,
      UNIT_MEI_TNSHK_KANA,
      UNIT_MEI_TNSHK_KANJI,
      KYU_GROUP_CODE,
      SHK_GROUP_CODE,
      SHK_UNIT_CODE,
      NINKA_SGY_KUBUN,
      HOMBU_SHSH_KUBUN,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      BLOCK_1,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_016"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_GROUPMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_GROUPMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      GROUP_CODE,
      KSHB,
      SHRYB,
      KYTS_GROUP_KUBUN,
      GROUP_BB_KUBUN,
      CHKS_KKT_KUBUN,
      GROUP_MEI_KANA,
      GROUP_MEI_TNSHK_KANA,
      GROUP_MEI_KANJI,
      GROUP_MEI_TNSHK_KANJI,
      KYTS_GROUP_CODE,
      KEIRI_GROUP_CODE,
      SHK_GROUP_CODE,
      TGT_SFSK_GROUP_CODE,
      SYSTM_UNYO_KIGYO_CODE,
      KANRI_SHK_GROUP_CODE,
      SHHN_TKTS_GROUP_CODE,
      GTSJ_KSSN_SHK_SSHK_CODE,
      HOMBU_TKTS_GROUP_CODE,
      KNRY_HNSH_BASHO_KUBUN,
      AREA_CODE,
      SUB_AREA_CODE,
      BTSRY_CNTR_HMTSK_KUBUN,
      KASHI_GYK_TTS_TRHKS_CODE,
      SHR_GYK_TTS_TRHKS_CODE,
      PLNT_CODE,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      BLOCK_1,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_017"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_SOSHIKIAREAMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_SOSHIKIAREAMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      AREA_CODE,
      SUB_AREA_CODE,
      KSHB,
      SHRYB,
      AREA_SHTSR,
      SUB_AREA_SHTSR,
      AREA_MEI_KANJI,
      AREA_MEI_TNSHK_KANJI,
      AREA_MEI_KANA,
      AREA_MEI_TNSHK_KANA,
      SUB_AREA_MEI_KANJI,
      SUB_AREA_MEI_TNSHK_KANJI,
      SUB_AREA_MEI_KANA,
      SUB_AREA_MEI_TNSHK_KANA,
      KNM_CODE,
      SYSTM_UNYO_KIGYO_CODE,
      HNSH_SHSH_KUBUN,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      BLOCK_1,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_018"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_SAISANKANRITANIMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_SAISANKANRITANIMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      SSNKN_TNCD,
      KSHB,
      SHRYB,
      SSN_KANRI_TNM_KANA,
      SSN_KANRI_TNM_KANJI,
      UNIT_CODE,
      SSN_HAIFU_CODE,
      SSN_KANRI_SHYK_CODE,
      KEIRI_TKSK_CODE,
      KIGYO_CODE,
      CTGRY_CODE,
      SUB_CTGRY_CODE,
      TBN_MSTR_KUBUN,
      TBN_TRK_CODE,
      CYKS_SHR_JYKN_GROUP_CODE,
      TNTSH_CODE,
      TRHKS_HNSHT_KUBUN,
      ZAIKO_HKT_KUBUN,
      DMPY_HAKKO_KUBUN_1,
      DMPY_HAKKO_KUBUN_2,
      URG_KNGK_KSN_KUBUN,
      SNDN_FUBAN_KUBUN,
      SHKK_ANNAI_SHTSR_KUBUN,
      YSHN_KANRI_KUBUN,
      TSKN_KUBUN,
      KEIRI_KNK_KSH_KUBUN,
      YSHN_SHK_KUBUN,
      TKSK_SHK_KUBUN,
      NNYSH_CODE,
      KYTS_TRHKS_CODE,
      GLN,
      SHR_GYK_TTS_TRHKS_CODE,
      KASHI_GYK_TTS_TRHKS_CODE,
      JURYO_KUBUN,
      TKB_SHK_TSH_KUBUN,
      SHHZ_KSHB_1,
      SHHZ_SHRYB_1,
      SHHZ_KUBUN_1,
      SHHZ_KSHB_2,
      SHHZ_SHRYB_2,
      SHHZ_KUBUN_2,
      SENYO_DMPY_GYOSU,
      TGK_SNDN_NO_KTS,
      EOS_SNDN_NO_KTS,
      SKYSH_MS_KUBUN,
      BTSRY_CNTR_CODE,
      DAI_2_BTSRY_CNTR_CODE,
      DAI3_BTSRY_CNTR_CODE,
      DAI4_BTSRY_CNTR_CODE,
      GYT_CODE,
      SMK_CODE,
      KEN_CODE,
      TSHKN_CODE,
      SCHSN_CODE,
      IDO,
      KEIDO,
      TKSK_MISHU_RANK_KUBUN,
      HMB_TANKA_SHIYO_KUBUN,
      SHNGR_HYOJI_KUBUN,
      CHNRT_KUBUN,
      BAIKA_HYOJI_KUBUN,
      BAIKA_SNSHT_KUBUN,
      HMPN_DMPY_HAKKO_KUBUN,
      TKSK_RDC_SHIYO_KUBUN,
      MTSMR_SKS_KUBUN,
      HOSHO_SHIYO_KUBUN,
      BCP_TAIO_KUBUN,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      BLOCK_1,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_019"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_KIGYOMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_KIGYOMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      KIGYO_CODE,
      KGYM_KANA,
      KGYM_KANJI,
      KIGYO_DHYSH_MEI_KANJI,
      KIGYO_DHYSH_MEI_KANA,
      GYT_CODE,
      SMK_CODE,
      KIGYO_GROUP_CODE,
      SUB_KIGYO_GROUP_CODE,
      TRHKS_HNSHT_KUBUN,
      BB_KYK_NO,
      KSHNJ_KUBUN,
      KSHNJ_KIGYO_CODE,
      NC_KIGYO_KUBUN,
      MY_NMBR,
      SHKN_GROUP_CODE,
      RNKTS_KSSN_KUBUN,
      RNKTS_KSSN_HKK_KIGYO_CODE,
      JIGYO_TSHSK_KUBUN,
      TKSK_TRKM_KUBUN,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_020"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_MAKERMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_MAKERMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      MAKER_CODE,
      MAKER_MEI_KANA,
      MAKER_MEI_KANJI,
      SHHN_SSK_KUBUN,
      MAKER_GROUP_CODE,
      NNK_MSTR_KUBUN,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_021"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_SYSTEMUNYOKIGYOMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_SYSTEMUNYOKIGYOMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      SYSTM_UNYO_KIGYO_CODE,
      KSHB,
      SHRYB,
      SYSTM_UNYO_KGYM_KANA,
      SYSTM_UNYO_KGYM_TNSHK_KANA,
      SYSTM_UNYO_KGYM_KANJI,
      SYSTM_UNYO_KGYM_TNSHK_KANJI,
      KSSNZ,
      CHKN_KSSNZ,
      CHUKI_KKK_KSH_NNGTS,
      CHUKI_KKK_SHRY_NNGTS,
      EIGYO_YOSAN_SKS_KSHZK,
      EIGYO_YOSAN_SKS_SHRYZ,
      KEIHI_YOSAN_SKS_KSHZK,
      KEIHI_YOSAN_SKS_SHRYZ,
      SHS_YOSAN_SKS_KSHZK,
      SHS_YOSAN_SKS_SHRYZ,
      OYA_SYSTM_UNYKG_CODE,
      KNM_CODE,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      BLOCK_1,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_022"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_TANTOSHAMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_TANTOSHAMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      UNIT_CODE,
      TNTSH_CODE,
      TNTSH_MEI_KANA,
      TNTSH_MEI_KANJI,
      SHAIN_CODE,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ
  #
  - taskId: "BAT_023"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_CATEGORYMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_CATEGORYMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      CTGRY_CODE,
      SUB_CTGRY_CODE,
      CTGRY_MEI_KANA,
      CTGRY_MEI_TNSHK_KANA,
      SUB_CTGRY_MEI_KANA,
      SUB_CTGRY_MEI_TNSHK_KANA,
      CTGRY_MEI_KANJI,
      CTGRY_MEI_TNSHK_KANJI,
      SUB_CTGRY_MEI_KANJI,
      SUB_CTGRY_MEI_TNSHK_KANJI,
      SHIYO_KNSH_KUBUN,
      RNK_JOTAI_KUBUN,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ

  - taskId: "BAT_024"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_SAISANKANRITANIFZKKMKMST_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_SAISANKANRITANIFZKKMKMST"
      truncateBeforeImport: true
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      SSNKN_TNCD,
      KSHB,
      SHRYB,
      SHYK_KIGYO_CODE,
      KGYM_KANJI_KIGYO_CODE,
      SHYK_KIGYO_GROUP_CODE,
      KGYM_KANJI_KIGYO_GROUP,
      SHYK_KIGYO_SUB_GROUP_CODE,
      KGYM_KANJI_KIGYO_SUB_GROUP,
      SHYK_GYT_CODE,
      SHYK_SMK_CODE,
      TRHKS_GYTM_KANJI,
      SHYK_GROUP_CODE,
      GROUP_MEI_KANJI,
      SHYK_AREA_CODE,
      AREA_MEI_KANJI,
      SHYK_SUB_AREA_CODE,
      SUB_AREA_MEI_KANJI,
      SHYK_CTGRY_CODE,
      CTGRY_MEI_KANJI,
      SHYK_SUB_CTGRY_CODE,
      SUB_CTGRY_MEI_KANJI,
      JIYU_SHYK_CODE_1,
      JIYU_SHYK_MEI_1,
      JIYU_SHYK_CODE_2,
      JIYU_SHYK_MEI_2,
      JIYU_SHYK_CODE_3,
      JIYU_SHYK_MEI_3,
      JIYU_SHYK_CODE_4,
      JIYU_SHYK_MEI_4,
      JIYU_SHYK_CODE_5,
      JIYU_SHYK_MEI_5,
      SSN_HANYO_KMK_1,
      SSN_HANYO_KMK_2,
      SSN_HANYO_KMK_3,
      SSN_HANYO_KMK_4,
      SSN_HANYO_KMK_5,
      SSN_HANYO_KMK_6,
      SSN_HANYO_KMK_7,
      SSN_HANYO_KMK_8,
      SSN_HANYO_KMK_9,
      SSN_HANYO_KMK_10,
      SSN_HANYO_KMK_11,
      SSN_HANYO_KMK_12,
      SSN_HANYO_KMK_13,
      SSN_HANYO_KMK_14,
      SSN_HANYO_KMK_15,
      SSN_HANYO_KMK_16,
      SSN_HANYO_KMK_17,
      SSN_HANYO_KMK_18,
      SSN_HANYO_KMK_19,
      SSN_HANYO_KMK_20,
      RNK_JOTAI_KUBUN,
      VRSN,
      SHIYO_NNGPP,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ

  - taskId: "BAT_025"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "M_JK_KENMUMASTER_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KYOTSU_MST/"
      supportsDynamicPath: true
    target:
      tableName: "M_JK_KENMUMASTER"
      truncateBeforeImport: true
      taihiSql: " select * from M_JK_KENMUMASTER where data_kubun='2' "
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      SYSTM_UNYO_KIGYO_CODE,
      SHAIN_CODE,
      UNIT_CODE,
      YKSHK_KUBUN,
      KMM_KUBUN,
      RNK_JOTAI_KUBUN,
      VRSN,
      RCRD_TRK_NCHJ,
      RCRD_KSHN_NCHJ,
      DATA_KUBUN
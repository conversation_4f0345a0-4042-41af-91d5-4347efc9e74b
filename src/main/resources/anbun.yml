# 按分バッチ設定ファイル
# 業務名: 按分バッチ

jobs:
  - taskId: "select_t_sk_shhn_pl_smmry_and_t_jinendo_kkk"
    source:
      query: |
        SELECT
          t.kssnz,
          m.tky_group_code,
          m.tky_area_code,
          t.maker_code,
          t.ssnkn_tncd,
          t.kanri_kk_toki_jssk_urgdk,
          t.kanri_kk_toki_jssk_zk_so_urd,
          t.kanri_kk_toki_jssk_ck_so_urd
        FROM
          t_jinendo_kkk m,
          t_sk_shhn_pl_smmry t
        ON
          t.ssnkn_tncd = m.ssnkn_tncd
        WHERE
          t.kssnz = ?
        AND
          m.nendo = ?

  - taskId: "export-monthly-performance"
    source:
      query: |
        SELECT 
          mp.plan_year,
          mp.plan_month,
          mp.department_code,
          d.department_name,
          mp.project_code,
          p.project_name,
          mp.planned_amount,
          mp.actual_amount,
          mp.cumulative_planned,
          mp.cumulative_actual,
          mp.achievement_rate,
          mp.created_at
        FROM monthly_performance mp
        LEFT JOIN m_department d ON mp.department_code = d.department_code
        LEFT JOIN m_project p ON mp.project_code = p.project_code
        WHERE mp.plan_year = ? 
          AND mp.plan_month >= ?
          AND mp.plan_month <= ?
        ORDER BY mp.plan_month, mp.department_code, mp.project_code

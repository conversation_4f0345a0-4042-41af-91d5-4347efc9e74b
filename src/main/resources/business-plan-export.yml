# 事業計画（直接）連携バッチ設定ファイル
# 業務名: 事業計画（直接）連携バッチ

jobs:
  - taskId: "export-business-plan-summary"
    source:
      query: |
        SELECT 
          bp.plan_year,
          bp.department_code,
          d.department_name,
          bp.project_code,
          p.project_name,
          bp.budget_amount,
          bp.actual_amount,
          bp.variance_amount,
          bp.variance_rate,
          bp.status,
          bp.created_at,
          bp.updated_at
        FROM business_plan bp
        LEFT JOIN m_department d ON bp.department_code = d.department_code
        LEFT JOIN m_project p ON bp.project_code = p.project_code
        WHERE bp.plan_year = ? 
          AND bp.status IN ('APPROVED', 'EXECUTING')
        ORDER BY bp.department_code, bp.project_code
      parameters:
        plan_year: "2024"
        # 以下のパラメータは任意で指定可能（共通条件として自動適用）
        # department_code: "SALES"     # 部門コードフィルタ
        # min_amount: 1000000          # 予算額閾値フィルタ
        # start_date: "2024-01-01"     # 日付範囲フィルタ（開始）
        # end_date: "2024-12-31"       # 日付範囲フィルタ（終了）
    target:
      bucketName: "business-plan-export-bucket"
      filePath: "business-plan/summary/business_plan_summary_${date}.csv"
      includeHeader: true
      encoding: "UTF-8"
      delimiter: ","
    fieldMappings:
      - sourceField: "plan_year"
        targetField: "計画年度"
        dataType: "String"
      - sourceField: "department_code"
        targetField: "部門コード"
        dataType: "String"
      - sourceField: "department_name"
        targetField: "部門名"
        dataType: "String"
      - sourceField: "project_code"
        targetField: "プロジェクトコード"
        dataType: "String"
      - sourceField: "project_name"
        targetField: "プロジェクト名"
        dataType: "String"
      - sourceField: "budget_amount"
        targetField: "予算金額"
        dataType: "Decimal"
      - sourceField: "actual_amount"
        targetField: "実績金額"
        dataType: "Decimal"
      - sourceField: "variance_amount"
        targetField: "差異金額"
        dataType: "Decimal"
      - sourceField: "variance_rate"
        targetField: "差異率"
        dataType: "Decimal"
      - sourceField: "status"
        targetField: "ステータス"
        dataType: "String"
      - sourceField: "created_at"
        targetField: "作成日時"
        dataType: "Timestamp"
      - sourceField: "updated_at"
        targetField: "更新日時"
        dataType: "Timestamp"

  - taskId: "export-monthly-performance"
    source:
      query: |
        SELECT 
          mp.plan_year,
          mp.plan_month,
          mp.department_code,
          d.department_name,
          mp.project_code,
          p.project_name,
          mp.planned_amount,
          mp.actual_amount,
          mp.cumulative_planned,
          mp.cumulative_actual,
          mp.achievement_rate,
          mp.created_at
        FROM monthly_performance mp
        LEFT JOIN m_department d ON mp.department_code = d.department_code
        LEFT JOIN m_project p ON mp.project_code = p.project_code
        WHERE mp.plan_year = ? 
          AND mp.plan_month >= ?
          AND mp.plan_month <= ?
        ORDER BY mp.plan_month, mp.department_code, mp.project_code
      parameters:
        plan_year: "2024"
        start_month: "01"
        end_month: "12"
    target:
      bucketName: "business-plan-export-bucket"
      filePath: "business-plan/monthly/monthly_performance_${date}.csv"
      includeHeader: true
      encoding: "UTF-8"
      delimiter: ","
    fieldMappings:
      - sourceField: "plan_year"
        targetField: "計画年度"
        dataType: "String"
      - sourceField: "plan_month"
        targetField: "計画月"
        dataType: "String"
      - sourceField: "department_code"
        targetField: "部門コード"
        dataType: "String"
      - sourceField: "department_name"
        targetField: "部門名"
        dataType: "String"
      - sourceField: "project_code"
        targetField: "プロジェクトコード"
        dataType: "String"
      - sourceField: "project_name"
        targetField: "プロジェクト名"
        dataType: "String"
      - sourceField: "planned_amount"
        targetField: "計画金額"
        dataType: "Decimal"
      - sourceField: "actual_amount"
        targetField: "実績金額"
        dataType: "Decimal"
      - sourceField: "cumulative_planned"
        targetField: "累計計画金額"
        dataType: "Decimal"
      - sourceField: "cumulative_actual"
        targetField: "累計実績金額"
        dataType: "Decimal"
      - sourceField: "achievement_rate"
        targetField: "達成率"
        dataType: "Decimal"
      - sourceField: "created_at"
        targetField: "作成日時"
        dataType: "Timestamp"

  - taskId: "export-budget-allocation"
    source:
      tableName: "budget_allocation"
      whereCondition: "fiscal_year = ? AND status = 'ACTIVE'"
      orderBy: "department_code, category_code"
      parameters:
        fiscal_year: "2024"
    target:
      bucketName: "business-plan-export-bucket"
      filePath: "business-plan/budget/budget_allocation_${date}.csv"
      includeHeader: true
      encoding: "UTF-8"
      delimiter: ","
    fieldMappings:
      - sourceField: "fiscal_year"
        targetField: "会計年度"
        dataType: "String"
      - sourceField: "department_code"
        targetField: "部門コード"
        dataType: "String"
      - sourceField: "category_code"
        targetField: "カテゴリコード"
        dataType: "String"
      - sourceField: "category_name"
        targetField: "カテゴリ名"
        dataType: "String"
      - sourceField: "allocated_amount"
        targetField: "配賦金額"
        dataType: "Decimal"
      - sourceField: "used_amount"
        targetField: "使用金額"
        dataType: "Decimal"
      - sourceField: "remaining_amount"
        targetField: "残額"
        dataType: "Decimal"
      - sourceField: "usage_rate"
        targetField: "使用率"
        dataType: "Decimal"
      - sourceField: "status"
        targetField: "ステータス"
        dataType: "String"
      - sourceField: "created_at"
        targetField: "作成日時"
        dataType: "Timestamp"
      - sourceField: "updated_at"
        targetField: "更新日時"
        dataType: "Timestamp"

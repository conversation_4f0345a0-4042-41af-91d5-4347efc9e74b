<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 統一されたコンソール出力設定 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- AWS SDK のログレベルを制御 -->
    <logger name="software.amazon.awssdk" level="WARN"/>
    <logger name="org.apache.http" level="WARN"/>

    <!-- HikariCP のログレベルを制御 -->
    <logger name="com.zaxxer.hikari" level="INFO"/>

    <!-- アプリケーションのログレベル -->
    <logger name="org.ms.bp" level="INFO"/>

    <!-- 統一されたルートロガー設定 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>

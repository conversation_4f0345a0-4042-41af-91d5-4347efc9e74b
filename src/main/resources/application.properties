# Batch Import System Configuration File
# Please adjust configuration values according to your environment

# Application Basic Settings
app.name=batch-import-system
app.version=1.0.0

# Active Profile
spring.profiles.active=dev

# Configuration Files
common.master.import.config.path=common-master-import.yml
business.plan.export.config.path=business-plan-export.yml

# S3 Import Configuration (for AWS RDS PostgreSQL S3 Import)
s3.import.aws.region=ap-northeast-1
aws.s3.bucket.name=ms-bp-files-dev-standard

# Database Settings
db.connection.timeout=5
db.parameter.prefix=/ms-bp/dev/standard/db

# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
# Local development settings (fallback when Parameter Store and Secrets Manager are not available)
# db.host=**************
# db.port=5432
# db.name=postgres
# db.username=postgres
# db.password=123456

db.host=127.0.0.1
db.port=13311
db.name=bpdatabase
db.username=bpadmin2025
db.password=qRrHKz5Lzz7Ko8lGyUaV

# SSH Tunnel configuration for connecting to AWS dev database
ssh.tunnel.enabled=true
ssh.tunnel.ssh.host=*************
ssh.tunnel.ssh.port=22
ssh.tunnel.ssh.username=ec2-user
ssh.tunnel.ssh.private.key.path=src/main/resources/ms-bp-key-ec2-dev.pem
ssh.tunnel.local.port=15432
ssh.tunnel.remote.host=ms-bp-db-dev-standard.cra8u4cw216c.ap-northeast-1.rds.amazonaws.com
ssh.tunnel.remote.port=5432
ssh.tunnel.connection.timeout=30000
ssh.tunnel.keep.alive.interval=60000

# Logging Settings
logging.level.root=INFO
logging.level.org.ms.bp=DEBUG

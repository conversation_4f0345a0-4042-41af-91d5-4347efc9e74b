# Production Environment Configuration
# Application Basic Settings
app.name=batch-import-system
app.version=1.0.0

# Active Profile
spring.profiles.active=prod
# Database Settings for Production
db.parameter.prefix=/ms-bp/prod/standard/db
# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
# SSH Tunnel configuration for connecting to AWS dev database
ssh.tunnel.enabled=false

aws.s3.bucket.name=ms-bp-files-prod-standard

# Logging Settings for Production
logging.level.root=WARN
logging.level.org.ms.bp=INFO

# 事業計画（間接）連携バッチ設定ファイル
# 業務名: 事業計画（間接）連携バッチ

jobs:
  - taskId: "export-business-plan-indirect-summary"
    source:
      query: |
        SELECT
          NENDO,
           '' ProductCode,
           '03' SettlementMonth,
          MAKER_CODE,
          SSNKN_TNCD,
          Sum(CASE WHEN KSSNZ = '04' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '05' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '06' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '07' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '08' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '09' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '10' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '11' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '12' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '01' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '02' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '03' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_12
        FROM
          T_KNSTS_RIEKI_KKK_AMBUN
        WHERE 
          NENDO = ?
        GROUP BY
          NENDO,
          MAKER_CODE,
          SSNKN_TNCD,
          ProductCode,
          SettlementMonth

    target:
      bucketName: "ms-bp-files-dev-standard"
      filePath: "export/business-plan/summary/business_plan_indirect_summary_$(Get-Date -Format 'yyyyMMdd').csv"
      includeHeader: true
      encoding: "UTF-8"
      delimiter: ","

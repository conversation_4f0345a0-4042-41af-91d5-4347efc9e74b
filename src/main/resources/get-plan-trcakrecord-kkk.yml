# 共通マスタ取得バッチ設定ファイル（S3インポート版）
# 業務名: 共通マスタ取得バッチ
# AWS RDS PostgreSQLのS3インポート機能を使用

aws:
  region: "ap-northeast-1"

jobs:
  - taskId: "BAT_009 計画"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "JK_KEIKAKU_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/KEIKAKU/"
      supportsDynamicPath: true
    target:
      tableName: "SSNKN_TN_C_CHKST_KKK"
      truncateBeforeImport: false
      deleteCondition: " Delete From SSNKN_TN_C_CHKST_KKK Where KANRI_KK_NENDO=?"
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      KANRI_KK_NENDO,
      TOGO__KUBUN,
      KANJO_KMKM,
      SSNKN_TNCD,
      GROUP_CODE,
      KKK_1_TSKM,
      KKK_2_TSKM,
      KKK_3_TSKM,
      KKK_4_TSKM,
      KKK_5_TSKM,
      KKK_6_TSKM,
      KKK_7_TSKM,
      KKK_8_TSKM,
      KKK_9_TSKM,
      KKK_10_TSKM,
      KKK_11_TSKM,
      KKK_12_TSKM,
      KKK_GK

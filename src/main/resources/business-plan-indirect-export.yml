# 事業計画（間接）連携バッチ設定ファイル
# 業務名: 事業計画（間接）連携バッチ

jobs:
  - taskId: "export-business-plan-indirect-summary"
    source:
      query: |
        SELECT
          NENDO,
           '' ProductCode,
           '03' SettlementMonth,
          MAKER_CODE,
          SSNKN_TNCD,
          Sum(CASE WHEN KSSNZ = '04' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '04' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_01,
          Sum(CASE WHEN KSSNZ = '05' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '05' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_02,
          Sum(CASE WHEN KSSNZ = '06' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '06' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_03,
          Sum(CASE WHEN KSSNZ = '07' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '07' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_04,
          Sum(CASE WHEN KSSNZ = '08' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '08' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_05,
          Sum(CASE WHEN KSSNZ = '09' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '09' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_06,
          Sum(CASE WHEN KSSNZ = '10' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '10' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_07,
          Sum(CASE WHEN KSSNZ = '11' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '11' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_08,
          Sum(CASE WHEN KSSNZ = '12' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '12' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_09,
          Sum(CASE WHEN KSSNZ = '01' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '01' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_10,
          Sum(CASE WHEN KSSNZ = '02' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '02' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_11,
          Sum(CASE WHEN KSSNZ = '03' THEN KKK_ZAIKO_RIEKI ELSE 0 END)        AS KKK_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN KKK_CHKS_RIEKI ELSE 0 END)         AS KKK_CHKS_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN TKBTS_ZAIKO_RIEKI ELSE 0 END)      AS TKBTS_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN TKBTS_CHKS_RIEKI ELSE 0 END)       AS TKBTS_CHKS_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN NNK_ZAIKO_RIEKI ELSE 0 END)        AS NNK_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN NNK_CHKS_RIEKI ELSE 0 END)         AS NNK_CHKS_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN SNTCHKST_ZAIKO_RIEKI ELSE 0 END)   AS SNTCHKST_ZAIKO_RIEKI_12,
          Sum(CASE WHEN KSSNZ = '03' THEN SNTCHKST_CHKS_RIEKI ELSE 0 END)    AS SNTCHKST_CHKS_RIEKI_12
        FROM
          T_KNSTS_RIEKI_KKK_AMBUN
        WHERE 
          NENDO = ?
        GROUP BY
          NENDO,
          MAKER_CODE,
          SSNKN_TNCD,
          ProductCode,
          SettlementMonth
      parameters:
        # NENDO: ""
        # 以下のパラメータは任意で指定可能（共通条件として自動適用）
        # department_code: "SALES"     # 部門コードフィルタ
        # min_amount: 1000000          # 予算額閾値フィルタ
        # start_date: "2024-01-01"     # 日付範囲フィルタ（開始）
        # end_date: "2024-12-31"       # 日付範囲フィルタ（終了）
    target:
      bucketName: "ms-bp-files-dev-standard"
      filePath: "export/business-plan/summary/business_plan_indirect_summary_$(Get-Date -Format 'yyyyMMdd').csv"
      includeHeader: true
      encoding: "UTF-8"
      delimiter: ","
    fieldMappings:
      - sourceField: "NENDO"
        targetField: "年度"
        dataType: "String"
      - sourceField: "SSNKN_TNCD"
        targetField: "採算管理単位コード"
        dataType: "String"
      - sourceField: "MAKER_CODE"
        targetField: "メーカーコード"
        dataType: "String"
      - sourceField: "ProductCode"
        targetField: "商品大分類コード"
        dataType: "String"
      - sourceField: "SettlementMonth"
        targetField: "決算月"
        dataType: "String"
      - sourceField: "KKK_ZAIKO_RIEKI_01"
        targetField: "企画＿在庫利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_01"
        targetField: "企画＿直送利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_01"
        targetField: "特別＿在庫利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_01"
        targetField: "特別＿直送利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_01"
        targetField: "年契＿在庫利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_01"
        targetField: "年契＿直送利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_01"
        targetField: "その他＿在庫利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_01"
        targetField: "その他＿直送利益＿０１月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_02"
        targetField: "企画＿在庫利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_02"
        targetField: "企画＿直送利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_02"
        targetField: "特別＿在庫利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_02"
        targetField: "特別＿直送利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_02"
        targetField: "年契＿在庫利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_02"
        targetField: "年契＿直送利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_02"
        targetField: "その他＿在庫利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_02"
        targetField: "その他＿直送利益＿０２月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_03"
        targetField: "企画＿在庫利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_03"
        targetField: "企画＿直送利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_03"
        targetField: "特別＿在庫利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_03"
        targetField: "特別＿直送利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_03"
        targetField: "年契＿在庫利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_03"
        targetField: "年契＿直送利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_03"
        targetField: "その他＿在庫利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_03"
        targetField: "その他＿直送利益＿０３月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_04"
        targetField: "企画＿在庫利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_04"
        targetField: "企画＿直送利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_04"
        targetField: "特別＿在庫利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_04"
        targetField: "特別＿直送利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_04"
        targetField: "年契＿在庫利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_04"
        targetField: "年契＿直送利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_04"
        targetField: "その他＿在庫利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_04"
        targetField: "その他＿直送利益＿０４月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_05"
        targetField: "企画＿在庫利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_05"
        targetField: "企画＿直送利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_05"
        targetField: "特別＿在庫利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_05"
        targetField: "特別＿直送利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_05"
        targetField: "年契＿在庫利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_05"
        targetField: "年契＿直送利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_05"
        targetField: "その他＿在庫利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_05"
        targetField: "その他＿直送利益＿０５月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_06"
        targetField: "企画＿在庫利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_06"
        targetField: "企画＿直送利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_06"
        targetField: "特別＿在庫利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_06"
        targetField: "特別＿直送利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_06"
        targetField: "年契＿在庫利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_06"
        targetField: "年契＿直送利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_06"
        targetField: "その他＿在庫利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_06"
        targetField: "その他＿直送利益＿０６月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_07"
        targetField: "企画＿在庫利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_07"
        targetField: "企画＿直送利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_07"
        targetField: "特別＿在庫利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_07"
        targetField: "特別＿直送利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_07"
        targetField: "年契＿在庫利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_07"
        targetField: "年契＿直送利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_07"
        targetField: "その他＿在庫利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_07"
        targetField: "その他＿直送利益＿０７月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_08"
        targetField: "企画＿在庫利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_08"
        targetField: "企画＿直送利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_08"
        targetField: "特別＿在庫利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_08"
        targetField: "特別＿直送利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_08"
        targetField: "年契＿在庫利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_08"
        targetField: "年契＿直送利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_08"
        targetField: "その他＿在庫利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_08"
        targetField: "その他＿直送利益＿０８月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_09"
        targetField: "企画＿在庫利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_09"
        targetField: "企画＿直送利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_09"
        targetField: "特別＿在庫利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_09"
        targetField: "特別＿直送利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_09"
        targetField: "年契＿在庫利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_09"
        targetField: "年契＿直送利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_09"
        targetField: "その他＿在庫利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_09"
        targetField: "その他＿直送利益＿０９月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_10"
        targetField: "企画＿在庫利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_10"
        targetField: "企画＿直送利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_10"
        targetField: "特別＿在庫利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_10"
        targetField: "特別＿直送利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_10"
        targetField: "年契＿在庫利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_10"
        targetField: "年契＿直送利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_10"
        targetField: "その他＿在庫利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_10"
        targetField: "その他＿直送利益＿１０月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_11"
        targetField: "企画＿在庫利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_11"
        targetField: "企画＿直送利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_11"
        targetField: "特別＿在庫利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_11"
        targetField: "特別＿直送利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_11"
        targetField: "年契＿在庫利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_11"
        targetField: "年契＿直送利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_11"
        targetField: "その他＿在庫利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_11"
        targetField: "その他＿直送利益＿１１月目"
        dataType: "Decimal"
      - sourceField: "KKK_ZAIKO_RIEKI_12"
        targetField: "企画＿在庫利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "KKK_CHKS_RIEKI_12"
        targetField: "企画＿直送利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_ZAIKO_RIEKI_12"
        targetField: "特別＿在庫利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "TKBTS_CHKS_RIEKI_12"
        targetField: "特別＿直送利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "NNK_ZAIKO_RIEKI_12"
        targetField: "年契＿在庫利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "NNK_CHKS_RIEKI_12"
        targetField: "年契＿直送利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_ZAIKO_RIEKI_12"
        targetField: "その他＿在庫利益＿１２月目"
        dataType: "Decimal"
      - sourceField: "SNTCHKST_CHKS_RIEKI_12"
        targetField: "その他＿直送利益＿１２月目"
        dataType: "Decimal"

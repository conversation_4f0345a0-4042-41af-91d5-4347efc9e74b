# 共通マスタ取得バッチ設定ファイル（S3インポート版）
# 業務名: 共通マスタ取得バッチ
# AWS RDS PostgreSQLのS3インポート機能を使用

aws:
  region: "ap-northeast-1"

jobs:
  - taskId: "BAT_009 実績"
    source:
      bucketName: "ms-bp-files-dev-standard"
      masterPhysicalName: "JK_JISSEKI_"
      defaultFilePath: "in/TO_JIGYOKEIKAKU/JISSEKI/"
      supportsDynamicPath: true
    target:
      tableName: "SSNKN_TN_C_CHKST_JSSK"
      truncateBeforeImport: false
      deleteCondition: " Delete From SSNKN_TN_C_CHKST_JSSK Where KANRI_KK_NENDO=?"
    importOptions:
      format: "CSV"
      delimiter: ","
      header: false
      encoding: "UTF-8"
    columnList: |
      KANRI_KK_NENDO,
      TOGO__KUBUN,
      KANJO_KMKM,
      SSNKN_TNCD,
      GROUP_CODE,
      JSSK_1_TSKM,
      JSSK_2_TSK<PERSON>,
      JSSK_3_TSKM,
      JSSK_4_TSKM,
      JSSK_5_TSKM,
      JSSK_6_TSKM,
      JSSK_7_TSKM,
      JSSK_8_TSKM,
      JSSK_9_TSKM,
      JSSK_10_TSKM,
      JSSK_11_TSKM,
      JSSK_12_TSKM,
      JSSK_GK

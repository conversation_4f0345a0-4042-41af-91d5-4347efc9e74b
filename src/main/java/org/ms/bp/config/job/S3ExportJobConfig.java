package org.ms.bp.config.job;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.ms.bp.config.source.DatabaseSourceConfig;
import org.ms.bp.config.source.S3TargetConfig;

/**
 * エクスポートジョブ設定
 */
public class S3ExportJobConfig {
    
    @JsonProperty("taskId")
    private String taskId;
    
    @JsonProperty("source")
    private DatabaseSourceConfig source;
    
    @JsonProperty("target")
    private S3TargetConfig target;

    public S3ExportJobConfig() {}

    public S3ExportJobConfig(String taskId, DatabaseSourceConfig source, S3TargetConfig target) {
        this.taskId = taskId;
        this.source = source;
        this.target = target;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    public DatabaseSourceConfig getSource() {
        return source;
    }
    
    public void setSource(DatabaseSourceConfig source) {
        this.source = source;
    }
    
    public S3TargetConfig getTarget() {
        return target;
    }
    
    public void setTarget(S3TargetConfig target) {
        this.target = target;
    }

    @Override
    public String toString() {
        return "ExportJobConfig{" +
                "taskId='" + taskId + '\'' +
                ", source=" + source +
                ", target=" + target +
                '}';
    }
}

package org.ms.bp.config.job;

/**
 * S3インポートジョブ設定クラス
 * AWS RDS PostgreSQLのS3インポート機能用の設定を保持
 */
public class S3ImportJobConfig {

    private String taskId;
    private S3ImportSource source;
    private S3ImportTarget target;
    private S3ImportOptions importOptions;
    private String columnList;

    // デフォルトコンストラクタ
    public S3ImportJobConfig() {}

    // Getters and Setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }

    public S3ImportSource getSource() { return source; }
    public void setSource(S3ImportSource source) { this.source = source; }

    public S3ImportTarget getTarget() { return target; }
    public void setTarget(S3ImportTarget target) { this.target = target; }

    public S3ImportOptions getImportOptions() { return importOptions; }
    public void setImportOptions(S3ImportOptions importOptions) { this.importOptions = importOptions; }

    public String getColumnList() { return columnList; }
    public void setColumnList(String columnList) { this.columnList = columnList; }

    /**
     * S3インポートソース設定
     */
    public static class S3ImportSource {
        private String bucketName;
        private String masterPhysicalName;
        private String defaultFilePath;
        private boolean supportsDynamicPath;

        // デフォルトコンストラクタ
        public S3ImportSource() {}
        // Getters and Setters
        public String getBucketName() { return bucketName; }
        public void setBucketName(String bucketName) { this.bucketName = bucketName; }

        public String getMasterPhysicalName() { return masterPhysicalName; }
        public void setMasterPhysicalName(String masterPhysicalName) { this.masterPhysicalName = masterPhysicalName; }

        public String getDefaultFilePath() { return defaultFilePath; }
        public void setDefaultFilePath(String defaultFilePath) { this.defaultFilePath = defaultFilePath; }

        public boolean isSupportsDynamicPath() { return supportsDynamicPath; }
        public void setSupportsDynamicPath(boolean supportsDynamicPath) { this.supportsDynamicPath = supportsDynamicPath; }
    }

    /**
     * S3インポートターゲット設定
     */
    public static class S3ImportTarget {
        private String tableName;
        private boolean truncateBeforeImport;
        private String deleteCondition;
        private String taihiSql;

        // デフォルトコンストラクタ
        public S3ImportTarget() {}

        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public boolean isTruncateBeforeImport() { return truncateBeforeImport; }
        public void setTruncateBeforeImport(boolean truncateBeforeImport) { this.truncateBeforeImport = truncateBeforeImport; }

        public String getDeleteCondition(){ return deleteCondition; }
        public void setDeleteCondition(String deleteCondition){ this.deleteCondition = deleteCondition; }

        public String getTaihiSql(){ return taihiSql; }
        public void setTaihiSql(String taihiSql){ this.taihiSql = taihiSql; }
    }

    /**
     * S3インポートオプション設定
     */
    public static class S3ImportOptions {
        private String format = "CSV";
        private String delimiter = ",";
        private boolean header = true;
        private String encoding = "UTF-8";

        // デフォルトコンストラクタ
        public S3ImportOptions() {}

        // Getters and Setters
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public String getDelimiter() { return delimiter; }
        public void setDelimiter(String delimiter) { this.delimiter = delimiter; }

        public boolean isHeader() { return header; }
        public void setHeader(boolean header) { this.header = header; }

        public String getEncoding() { return encoding; }
        public void setEncoding(String encoding) { this.encoding = encoding; }
    }
}

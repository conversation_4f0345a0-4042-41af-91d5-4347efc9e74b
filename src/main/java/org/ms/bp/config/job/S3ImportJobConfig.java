package org.ms.bp.config.job;

import lombok.Getter;
import lombok.Setter;

/**
 * S3インポートジョブ設定クラス
 * AWS RDS PostgreSQLのS3インポート機能用の設定を保持
 */
@Setter
@Getter
public class S3ImportJobConfig {

    // Getters and Setters
    private String taskId;
    private S3ImportSource source;
    private S3ImportTarget target;
    private S3ImportOptions importOptions;
    private String columnList;

    // デフォルトコンストラクタ
    public S3ImportJobConfig() {}

    /**
     * S3インポートソース設定
     */
    @Setter
    @Getter
    public static class S3ImportSource {
        // Getters and Setters
        private String bucketName;
        private String masterPhysicalName;
        private String defaultFilePath;
        private boolean supportsDynamicPath;

        // デフォルトコンストラクタ
        public S3ImportSource() {}

    }

    /**
     * S3インポートターゲット設定
     */
    @Setter
    @Getter
    public static class S3ImportTarget {
        // Getters and Setters
        private String tableName;
        private boolean truncateBeforeImport;
        private String deleteCondition;
        private String taihiSql;

        // デフォルトコンストラクタ
        public S3ImportTarget() {}

    }

    /**
     * S3インポートオプション設定
     */
    public static class S3ImportOptions {
        private String format = "CSV";
        private String delimiter = ",";
        private boolean header = true;
        private String encoding = "UTF-8";

        // デフォルトコンストラクタ
        public S3ImportOptions() {}

        // Getters and Setters
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public String getDelimiter() { return delimiter; }
        public void setDelimiter(String delimiter) { this.delimiter = delimiter; }

        public boolean isHeader() { return header; }
        public void setHeader(boolean header) { this.header = header; }

        public String getEncoding() { return encoding; }
        public void setEncoding(String encoding) { this.encoding = encoding; }
    }
}

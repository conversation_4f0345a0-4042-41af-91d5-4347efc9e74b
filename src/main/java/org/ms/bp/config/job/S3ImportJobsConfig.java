package org.ms.bp.config.job;

import java.util.List;

/**
 * S3インポートジョブ設定集合クラス
 * 複数のS3インポートジョブ設定を保持
 */
public class S3ImportJobsConfig {

    private AwsConfig aws;
    private List<S3ImportJobConfig> jobs;

    // デフォルトコンストラクタ
    public S3ImportJobsConfig() {}

    // Getters and Setters
    public AwsConfig getAws() { return aws; }
    public void setAws(AwsConfig aws) { this.aws = aws; }

    public List<S3ImportJobConfig> getJobs() { return jobs; }
    public void setJobs(List<S3ImportJobConfig> jobs) { this.jobs = jobs; }

    /**
     * AWS設定クラス
     */
    public static class AwsConfig {
        private String region = "ap-northeast-1";

        // デフォルトコンストラクタ
        public AwsConfig() {}

        // Getters and Setters
        public String getRegion() { return region; }
        public void setRegion(String region) { this.region = region; }
    }

    /**
     * 設定の妥当性を検証
     */
    public void validate() {
        if (aws == null) {
            throw new IllegalArgumentException("AWS設定が見つかりません");
        }

        if (aws.getRegion() == null || aws.getRegion().trim().isEmpty()) {
            throw new IllegalArgumentException("AWSリージョンが設定されていません");
        }

        if (jobs == null || jobs.isEmpty()) {
            throw new IllegalArgumentException("インポートジョブが設定されていません");
        }

        for (S3ImportJobConfig job : jobs) {
            validateJob(job);
        }
    }

    /**
     * 個別ジョブの妥当性を検証
     */
    private void validateJob(S3ImportJobConfig job) {
        if (job.getTaskId() == null || job.getTaskId().trim().isEmpty()) {
            throw new IllegalArgumentException("タスクIDが設定されていません");
        }

        if (job.getSource() == null) {
            throw new IllegalArgumentException("ソース設定が見つかりません: " + job.getTaskId());
        }

        if (job.getTarget() == null) {
            throw new IllegalArgumentException("ターゲット設定が見つかりません: " + job.getTaskId());
        }

        if (job.getColumnList() == null || job.getColumnList().trim().isEmpty()) {
            throw new IllegalArgumentException("カラムリストが設定されていません: " + job.getTaskId());
        }

        // ソース設定の検証
        var source = job.getSource();
        if (source.getMasterPhysicalName() == null || source.getMasterPhysicalName().trim().isEmpty()) {
            throw new IllegalArgumentException("マスタ物理名が設定されていません: " + job.getTaskId());
        }

        // ターゲット設定の検証
        var target = job.getTarget();
        if (target.getTableName() == null || target.getTableName().trim().isEmpty()) {
            throw new IllegalArgumentException("テーブル名が設定されていません: " + job.getTaskId());
        }
    }
}

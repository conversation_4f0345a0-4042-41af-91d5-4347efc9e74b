package org.ms.bp.config.job;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * エクスポートジョブ設定のルートクラス
 */
@Setter
@Getter
public class S3ExportJobsConfig {
    
    @JsonProperty("jobs")
    private List<S3ExportJobConfig> jobs;
    
    public S3ExportJobsConfig() {}
    
    public S3ExportJobsConfig(List<S3ExportJobConfig> jobs) {
        this.jobs = jobs;
    }

    @Override
    public String toString() {
        return "ExportJobsConfig{" +
                "jobs=" + jobs +
                '}';
    }
}

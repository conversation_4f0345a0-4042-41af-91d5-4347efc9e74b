package org.ms.bp.config.job;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * エクスポートジョブ設定のルートクラス
 */
public class S3ExportJobsConfig {
    
    @JsonProperty("jobs")
    private List<S3ExportJobConfig> jobs;
    
    public S3ExportJobsConfig() {}
    
    public S3ExportJobsConfig(List<S3ExportJobConfig> jobs) {
        this.jobs = jobs;
    }
    
    public List<S3ExportJobConfig> getJobs() {
        return jobs;
    }
    
    public void setJobs(List<S3ExportJobConfig> jobs) {
        this.jobs = jobs;
    }
    
    @Override
    public String toString() {
        return "ExportJobsConfig{" +
                "jobs=" + jobs +
                '}';
    }
}

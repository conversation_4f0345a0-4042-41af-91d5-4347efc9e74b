package org.ms.bp.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * S3ターゲット設定
 */
@Setter
@Getter
public class S3TargetConfig {

    @JsonProperty("filePath")
    private String filePath;
    
    @JsonProperty("includeHeader")
    private boolean includeHeader = true;
    
    @JsonProperty("encoding")
    private String encoding = "UTF-8";
    
    @JsonProperty("delimiter")
    private String delimiter = ",";
    
    public S3TargetConfig() {}
    
    public S3TargetConfig(String bucketName, String filePath) {
        this.filePath = filePath;
    }

    @Override
    public String toString() {
        return "S3TargetConfig{" +
                ", filePath='" + filePath + '\'' +
                ", includeHeader=" + includeHeader +
                ", encoding='" + encoding + '\'' +
                ", delimiter='" + delimiter + '\'' +
                '}';
    }
}

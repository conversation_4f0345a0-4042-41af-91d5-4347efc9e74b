package org.ms.bp.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * S3ターゲット設定
 */
public class S3TargetConfig {

    @JsonProperty("filePath")
    private String filePath;
    
    @JsonProperty("includeHeader")
    private boolean includeHeader = true;
    
    @JsonProperty("encoding")
    private String encoding = "UTF-8";
    
    @JsonProperty("delimiter")
    private String delimiter = ",";
    
    public S3TargetConfig() {}
    
    public S3TargetConfig(String bucketName, String filePath) {
        this.filePath = filePath;
    }

    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public boolean isIncludeHeader() {
        return includeHeader;
    }
    
    public void setIncludeHeader(boolean includeHeader) {
        this.includeHeader = includeHeader;
    }
    
    public String getEncoding() {
        return encoding;
    }
    
    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }
    
    public String getDelimiter() {
        return delimiter;
    }
    
    public void setDelimiter(String delimiter) {
        this.delimiter = delimiter;
    }
    
    @Override
    public String toString() {
        return "S3TargetConfig{" +
                ", filePath='" + filePath + '\'' +
                ", includeHeader=" + includeHeader +
                ", encoding='" + encoding + '\'' +
                ", delimiter='" + delimiter + '\'' +
                '}';
    }
}

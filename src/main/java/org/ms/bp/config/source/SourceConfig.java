package org.ms.bp.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * S3ソース設定
 */
public class SourceConfig {

    @JsonProperty("bucketName")
    private String bucketName;

    @JsonProperty("masterPhysicalName")
    private String masterPhysicalName;

    @JsonProperty("defaultFilePath")
    private String defaultFilePath;

    @JsonProperty("supportsDynamicPath")
    private boolean supportsDynamicPath = false;

    public SourceConfig() {}

    public SourceConfig(String bucketName, String masterPhysicalName, String defaultFilePath) {
        this.bucketName = bucketName;
        this.masterPhysicalName = masterPhysicalName;
        this.defaultFilePath = defaultFilePath;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getMasterPhysicalName() {
        return masterPhysicalName;
    }

    public void setMasterPhysicalName(String masterPhysicalName) {
        this.masterPhysicalName = masterPhysicalName;
    }

    public String getDefaultFilePath() {
        return defaultFilePath;
    }

    public void setDefaultFilePath(String defaultFilePath) {
        this.defaultFilePath = defaultFilePath;
    }

    public boolean isSupportsDynamicPath() {
        return supportsDynamicPath;
    }

    public void setSupportsDynamicPath(boolean supportsDynamicPath) {
        this.supportsDynamicPath = supportsDynamicPath;
    }

    @Override
    public String toString() {
        return "SourceConfig{" +
                "bucketName='" + bucketName + '\'' +
                ", masterPhysicalName='" + masterPhysicalName + '\'' +
                ", defaultFilePath='" + defaultFilePath + '\'' +
                ", supportsDynamicPath=" + supportsDynamicPath +
                '}';
    }
}

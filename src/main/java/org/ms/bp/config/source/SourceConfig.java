package org.ms.bp.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * S3ソース設定
 */
@Setter
@Getter
public class SourceConfig {

    @JsonProperty("masterPhysicalName")
    private String masterPhysicalName;

    @JsonProperty("defaultFilePath")
    private String defaultFilePath;

    @JsonProperty("supportsDynamicPath")
    private boolean supportsDynamicPath = false;

    public SourceConfig() {}

    public SourceConfig(String masterPhysicalName, String defaultFilePath) {
        this.masterPhysicalName = masterPhysicalName;
        this.defaultFilePath = defaultFilePath;
    }

    @Override
    public String toString() {
        return "SourceConfig{" +
                "masterPhysicalName='" + masterPhysicalName + '\'' +
                ", defaultFilePath='" + defaultFilePath + '\'' +
                ", supportsDynamicPath=" + supportsDynamicPath +
                '}';
    }
}

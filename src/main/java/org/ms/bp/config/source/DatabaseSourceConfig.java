package org.ms.bp.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * データベースソース設定
 */
public class DatabaseSourceConfig {
    
    @JsonProperty("tableName")
    private String tableName;
    
    @JsonProperty("query")
    private String query;
    
    @JsonProperty("whereCondition")
    private String whereCondition;
    
    @JsonProperty("orderBy")
    private String orderBy;
    
    @JsonProperty("parameters")
    private Map<String, Object> parameters;

    public DatabaseSourceConfig() {}
    
    public DatabaseSourceConfig(String tableName, String query) {
        this.tableName = tableName;
        this.query = query;
    }
    
    public String getTableName() {
        return tableName;
    }
    
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
    
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
    }
    
    public String getWhereCondition() {
        return whereCondition;
    }
    
    public void setWhereCondition(String whereCondition) {
        this.whereCondition = whereCondition;
    }
    
    public String getOrderBy() {
        return orderBy;
    }
    
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }
    
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    @Override
    public String toString() {
        return "DatabaseSourceConfig{" +
                "tableName='" + tableName + '\'' +
                ", query='" + query + '\'' +
                ", whereCondition='" + whereCondition + '\'' +
                ", orderBy='" + orderBy + '\'' +
                ", parameters=" + parameters +
                '}';
    }
}

package org.ms.bp.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * データベースソース設定
 */
@Setter
@Getter
public class DatabaseSourceConfig {
    
    @JsonProperty("tableName")
    private String tableName;
    
    @JsonProperty("query")
    private String query;
    
    @JsonProperty("whereCondition")
    private String whereCondition;
    
    @JsonProperty("orderBy")
    private String orderBy;
    
    @JsonProperty("parameters")
    private Map<String, Object> parameters;

    public DatabaseSourceConfig() {}
    
    public DatabaseSourceConfig(String tableName, String query) {
        this.tableName = tableName;
        this.query = query;
    }

    @Override
    public String toString() {
        return "DatabaseSourceConfig{" +
                "tableName='" + tableName + '\'' +
                ", query='" + query + '\'' +
                ", whereCondition='" + whereCondition + '\'' +
                ", orderBy='" + orderBy + '\'' +
                ", parameters=" + parameters +
                '}';
    }
}

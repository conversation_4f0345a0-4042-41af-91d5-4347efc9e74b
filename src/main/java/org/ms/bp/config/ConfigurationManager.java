package org.ms.bp.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.ms.bp.config.job.S3ExportJobsConfig;
import org.ms.bp.config.job.S3ImportJobsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 設定管理クラス
 * 環境に応じた設定ファイルから設定値を読み込む
 * 環境変数での上書きもサポート
 */
public class ConfigurationManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    
    private static ConfigurationManager instance;
    private final Properties properties;
    private final ObjectMapper yamlMapper;

    // 環境変数名
    private static final String ENV_PROFILE = "SPRING_PROFILES_ACTIVE";

    // デフォルト設定
    private final String DEFAULT_PROFILE = "dev";
    private final String DEFAULT_CONF_FILE_NANE = "application.properties";
    
    /**
     * プライベートコンストラクタ（シングルトンパターン）
     */
    private ConfigurationManager() {
        this.properties = new Properties();
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
        this.yamlMapper.registerModule(new JavaTimeModule());
        loadConfiguration();
    }
    
    /**
     * シングルトンインスタンスを取得
     * @return ConfigurationManagerインスタンス
     */
    public static synchronized ConfigurationManager getInstance() {
        if (instance == null) {
            instance = new ConfigurationManager();
        }
        return instance;
    }
    
    /**
     * 設定ファイルを読み込む
     */
    private void loadConfiguration() {
        // アクティブプロファイルを取得
        String activeProfile = getActiveProfile();
        logger.info("アクティブプロファイル: {}", activeProfile);
        
        // デフォルト設定を読み込み
        loadPropertiesFile(DEFAULT_CONF_FILE_NANE);

        // 環境固有の設定を読み込み（存在する場合）
        if (!DEFAULT_PROFILE.equals(activeProfile)) {
            String profileSpecificFile = "application-" + activeProfile + ".properties";
            loadPropertiesFile(profileSpecificFile);
        }
        
        logger.info("設定ファイルの読み込みが完了しました。読み込まれた設定数: {}", properties.size());
    }
    
    /**
     * プロパティファイルを読み込む
     * @param fileName ファイル名
     */
    private void loadPropertiesFile(String fileName) {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream != null) {
                properties.load(inputStream);
                logger.debug("設定ファイルを読み込みました: {}", fileName);
            } else {
                if (fileName.equals(DEFAULT_CONF_FILE_NANE)) {
                    throw new RuntimeException("必須設定ファイルが見つかりません: " + fileName);
                }
                logger.warn("設定ファイルが見つかりません: {}", fileName);
            }
        } catch (IOException e) {
            logger.error("設定ファイルの読み込みエラー: {}", fileName, e);
            throw new RuntimeException("設定ファイルの読み込みに失敗しました: " + fileName, e);
        }
    }
    
    /**
     * アクティブプロファイルを取得
     * @return アクティブプロファイル
     */
    private String getActiveProfile() {
        // 環境変数から取得、なければデフォルト
        String profile = System.getenv(ENV_PROFILE);
        if (profile == null || profile.trim().isEmpty()) {
            profile = System.getProperty("spring.profiles.active", DEFAULT_PROFILE);
        }
        return profile.trim().toLowerCase();
    }
    
    /**
     * 設定値を取得（デフォルト値なし、必須）
     * @param key 設定キー
     * @return 設定値
     * @throws RuntimeException 設定が見つからない場合
     */
    public String getProperty(String key) {
        String value = getProperty(key, null);
        if (value == null) {
            throw new RuntimeException("必須設定が見つかりません: " + key);
        }
        return value;
    }
    
    /**
     * 設定値を取得（デフォルト値付き、環境変数での上書きをサポート）
     * @param key 設定キー
     * @param defaultValue デフォルト値
     * @return 設定値
     */
    public String getProperty(String key, String defaultValue) {
        // 1. 環境変数から取得を試行（キーを大文字に変換し、ドットをアンダースコアに変換）
        String envKey = key.toUpperCase().replace('.', '_');
        String envValue = System.getenv(envKey);
        if (envValue != null && !envValue.trim().isEmpty()) {
            logger.debug("環境変数から設定値を取得: {} = {}", envKey, envValue);
            return envValue.trim();
        }
        
        // 2. プロパティファイルから取得
        String propValue = properties.getProperty(key);
        if (propValue != null && !propValue.trim().isEmpty()) {
            logger.debug("設定ファイルから設定値を取得: {} = {}", key, propValue);
            return propValue.trim();
        }
        
        // 3. デフォルト値を返す
        if (defaultValue != null) {
            logger.debug("デフォルト値を使用: {} = {}", key, defaultValue);
            return defaultValue;
        }
        
        return null;
    }
    
    /**
     * 整数値を取得
     * @param key 設定キー
     * @param defaultValue デフォルト値
     * @return 整数値
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key, String.valueOf(defaultValue));
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("設定値が整数ではありません: {} = {}, デフォルト値を使用: {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * ブール値を取得
     * @param key 設定キー
     * @param defaultValue デフォルト値
     * @return ブール値
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key, String.valueOf(defaultValue));
        return Boolean.parseBoolean(value);
    }
    
    /**
     * 全ての設定をログ出力（デバッグ用）
     * 機密情報は出力しない
     */
    public void logConfiguration() {
        if (logger.isDebugEnabled()) {
            logger.debug("=== 設定情報 ===");
            properties.entrySet().stream()
                .filter(entry -> !isSensitiveKey(entry.getKey().toString()))
                .forEach(entry -> logger.debug("{} = {}", entry.getKey(), entry.getValue()));
            logger.debug("===============");
        }
    }
    
    /**
     * 機密情報のキーかどうかをチェック
     * @param key 設定キー
     * @return 機密情報の場合true
     */
    private boolean isSensitiveKey(String key) {
        String lowerKey = key.toLowerCase();
        String[] keywords = {"password", "secret", "key", "token", "credential"};
        
        for (String keyword : keywords) {
            if (lowerKey.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    

    
    /**
     * YAMLファイルからエクスポートジョブ設定を読み込む
     *
     * @param configFilePath 設定ファイルのパス
     * @return エクスポートジョブ設定
     * @throws IOException ファイル読み込みエラー
     */
    public S3ExportJobsConfig loadExportJobsConfig(String configFilePath) throws IOException {
        logger.info("エクスポート設定ファイルを読み込み中: {}", configFilePath);

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(configFilePath)) {
            if (inputStream == null) {
                throw new IOException("設定ファイルが見つかりません: " + configFilePath);
            }
            S3ExportJobsConfig config = yamlMapper.readValue(inputStream, S3ExportJobsConfig.class);
            logger.info("エクスポート設定ファイルの読み込み完了。ジョブ数: {}", config.getJobs().size());
            return config;
        }
    }

    /**
     * YAMLファイルからS3インポートジョブ設定を読み込む
     *
     * @param configFilePath 設定ファイルのパス
     * @return S3インポートジョブ設定
     * @throws IOException ファイル読み込みエラー
     */
    public S3ImportJobsConfig loadS3ImportJobsConfig(String configFilePath) throws IOException {
        logger.info("S3インポート設定ファイルを読み込み中: {}", configFilePath);

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(configFilePath)) {
            if (inputStream == null) {
                throw new IOException("設定ファイルが見つかりません: " + configFilePath);
            }
            S3ImportJobsConfig config = yamlMapper.readValue(inputStream, S3ImportJobsConfig.class);
            logger.info("S3インポート設定ファイルの読み込み完了。ジョブ数: {}", config.getJobs().size());
            return config;
        }
    }

    /**
     * エクスポートジョブ設定の妥当性をチェック
     *
     * @param config エクスポートジョブ設定
     * @throws IllegalArgumentException 設定が無効な場合
     */
    public void validateConfig(S3ExportJobsConfig config) {
        if (config == null || config.getJobs() == null || config.getJobs().isEmpty()) {
            throw new IllegalArgumentException("ジョブ設定が空です");
        }

        for (var job : config.getJobs()) {
            if (job.getTaskId() == null || job.getTaskId().trim().isEmpty()) {
                throw new IllegalArgumentException("タスクIDが設定されていません");
            }

            if (job.getSource() == null) {
                throw new IllegalArgumentException("ソース設定が設定されていません: " + job.getTaskId());
            }

            if (job.getTarget() == null) {
                throw new IllegalArgumentException("ターゲット設定が設定されていません: " + job.getTaskId());
            }

            if (job.getTarget().getFilePath() == null || job.getTarget().getFilePath().trim().isEmpty()) {
                throw new IllegalArgumentException("ファイルパスが設定されていません: " + job.getTaskId());
            }
        }
    }

    /**
     * S3インポートジョブ設定の妥当性をチェック
     *
     * @param config S3インポートジョブ設定
     * @throws IllegalArgumentException 設定が無効な場合
     */
    public void validateConfig(S3ImportJobsConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("S3インポート設定が空です");
        }

        // 内部のvalidateメソッドを呼び出し
        config.validate();
    }
}

package org.ms.bp.config.mapping;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * CSVフィールドとデータベースフィールドのマッピング設定
 */
public class FieldMapping {
    
    @JsonProperty("sourceField")
    private String sourceField;
    
    @JsonProperty("targetField")
    private String targetField;
    
    @JsonProperty("dataType")
    private String dataType;
    
    public FieldMapping() {}
    
    public FieldMapping(String sourceField, String targetField, String dataType) {
        this.sourceField = sourceField;
        this.targetField = targetField;
        this.dataType = dataType;
    }
    
    public String getSourceField() {
        return sourceField;
    }
    
    public void setSourceField(String sourceField) {
        this.sourceField = sourceField;
    }
    
    public String getTargetField() {
        return targetField;
    }
    
    public void setTargetField(String targetField) {
        this.targetField = targetField;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    @Override
    public String toString() {
        return "FieldMapping{" +
                "sourceField='" + sourceField + '\'' +
                ", targetField='" + targetField + '\'' +
                ", dataType='" + dataType + '\'' +
                '}';
    }
}

package org.ms.bp.runner;

import org.ms.bp.config.ConfigurationManager;
import org.ms.bp.config.job.S3ImportJobConfig;
import org.ms.bp.config.job.S3ImportJobsConfig;
import org.ms.bp.service.executor.ImportJobExecutor;
import org.ms.bp.service.integration.aws.S3ImportService;
import org.ms.bp.util.MessageCodeList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

public class GetPlanTrackRecordRunner {

    private static final Logger logger = LoggerFactory.getLogger(GetPlanTrackRecordRunner.class);

    // 設定ファイルパス（固定）
    private static final String CONFIG_FILE_PATH_KKK = "get-plan-trcakrecord-kkk.yml";
    private static final String CONFIG_FILE_PATH_JSSK = "get-plan-trcakrecord-jssk.yml";
    private static final String SAISAN_KANRI_TANI_KEIKAKU = "1";
    private static final String SAISAN_KANRI_TANI_JISEKI = "2";

    // 設定管理器
    private final ConfigurationManager config;
    private final String[] outArgs = new String[3];

    private String fileType;
    private String targetDate;
    private String s3Path;
    //private String tableName;
    private String logComment;
    private final String[] args;

    /**
     * コンストラクタ（起動パラメーター付き）
     */
    public GetPlanTrackRecordRunner(String[] args) {

        this.args = args;

        // 設定管理器を初期化
        this.config = ConfigurationManager.getInstance();

        // 設定情報をログ出力（デバッグ用）
        config.logConfiguration();

        // MDCにアプリケーション情報を設定（構造化ログ用）
        setupLoggingContext();
    }

    /**
     * ログコンテキストを設定
     */
    private void setupLoggingContext() {
        MDC.put("application", "getPlanTrackRecord-batch");
        MDC.put("businessProcess", "計画・実績取得バッチ");
        MDC.put("environment", config.getProperty("app.profile", "dev"));
        MDC.put("version", config.getProperty("app.version", "1.0.0"));

    }

    public int run() {
        try {

            for (String arg : args) {
                if (arg != null && arg.startsWith("fileType=")) {
                    fileType = arg;
                }
                if (arg != null && arg.startsWith("targetDate=")) {
                    targetDate = arg;
                }
                if (arg != null && arg.startsWith("s3Path=")) {
                    s3Path = arg;
                }
            }

            // テーブル名称設定
            if(fileType.substring("fileType=".length()).equals(SAISAN_KANRI_TANI_KEIKAKU))
            {
                // 採算管理単位C別_直接_計画
                //tableName="SSNKN_TN_C_CHKST_KKK";
                logComment = "BAT_009 計画";
            }else{
                // 採算管理単位C別_直接_実績
                //tableName="SSNKN_TN_C_CHKST_JSSK";
                logComment = "BAT_009 実績";
            }
            // 開始ログ出力
            logger.info(MessageFormat.format(MessageCodeList.INF_001, logComment));

            // パラメータ設定
            SetParams();

            // 1. 設定値を使用
            if(fileType.substring("fileType=".length()).equals(SAISAN_KANRI_TANI_KEIKAKU))
            {
                // 採算管理単位C別_直接_計画
                logger.info("設定ファイル: {}", CONFIG_FILE_PATH_KKK);
            }else{
                // 採算管理単位C別_直接_実績
                logger.info("設定ファイル: {}", CONFIG_FILE_PATH_JSSK);
            }

            // 2. S3インポート設定ファイルを読み込み
            S3ImportJobsConfig jobsConfig;
            if(fileType.substring("fileType=".length()).equals(SAISAN_KANRI_TANI_KEIKAKU))
            {
                // 採算管理単位C別_直接_計画
                jobsConfig = config.loadS3ImportJobsConfig(CONFIG_FILE_PATH_KKK);
            }else{
                // 採算管理単位C別_直接_実績
                jobsConfig = config.loadS3ImportJobsConfig(CONFIG_FILE_PATH_JSSK);
            }
            config.validateConfig(jobsConfig);

            // 3. AWSリージョンを取得
            String awsRegion = jobsConfig.getAws().getRegion();
            logger.info("AWSリージョン: {}", awsRegion);

            outArgs[0] = fileType;
            outArgs[1] = targetDate;
            outArgs[2] = s3Path;
            // 4. S3インポートサービスを初期化
            S3ImportService s3ImportService = new S3ImportService(awsRegion);
            ImportJobExecutor jobExecutor = new ImportJobExecutor(s3ImportService, outArgs);

            // 5. 全ジョブを実行
            return executeS3ImportJobs(jobExecutor, jobsConfig.getJobs());
        } catch (Exception e) {
            logger.error("計画・実績取得バッチ実行エラー", e);
            return 1;
        }
    }

    private void SetParams()
    {
        // パラメータ.対象年月日が指定されていない場合
        if(targetDate.isEmpty())
        {
            // システム日付（YYYYMMDD形式）
            LocalDateTime nowDate = LocalDateTime.now();
            targetDate = "targetDate=" + DateTimeFormatter.ofPattern("yyyyMMdd").format(nowDate);
        }

        // パラメータ.S3パスが指定されていない場合
        if(s3Path.isEmpty())
        {
            // ファイル種別ごとのデフォルトパス
            if(fileType.substring("fileType=".length()).equals(SAISAN_KANRI_TANI_KEIKAKU))
            {
                // 採算管理単位C別_直接_計画
                s3Path="s3Path=in/TO_JIGYOKEIKAKU/KEIKAKU";
            }else{
                // 採算管理単位C別_直接_実績
                s3Path="s3Path=in/TO_JIGYOKEIKAKU/JISSEKI";
            }
        }
    }

    /**
     * S3インポートジョブを実行
     */
    private int executeS3ImportJobs(ImportJobExecutor jobExecutor, List<S3ImportJobConfig> jobs) {
        logger.info("=== 計画・実績取得S3インポートジョブ実行開始 (ジョブ数: {}) ===", jobs.size());

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        for (var job : jobs) {
            try {
                logger.info("計画・実績取得データS3インポート開始: {}", job.getTaskId());
                ImportJobExecutor.JobExecutionResult result = jobExecutor.executeJob(job);

                if (result.isSuccess()) {
                    logger.info(MessageFormat.format(MessageCodeList.INF_002, logComment, "Success"));
                    successCount.incrementAndGet();
                    logger.info(MessageFormat.format(MessageCodeList.INF_005, result.getImportedRows()));
                    //logger.info("計画・実績取得データS3インポート成功: {} (インポート件数: {})",
                    //        job.getTaskId(), result.getImportedRows());
                } else {
                    failureCount.incrementAndGet();
                    logger.error("計画・実績取得データS3インポート失敗: {} - {}", job.getTaskId(), result.getErrorMessage());
                }

            } catch (Exception e) {
                logger.error("計画・実績取得データS3インポート中に予期しないエラー: {}", job.getTaskId(), e);
                failureCount.incrementAndGet();
            }
        }

        logExecutionSummary(jobs.size(), successCount.get(), failureCount.get());
        return failureCount.get() > 0 ? 1 : 0;
    }

    /**
     * 実行結果サマリーをログ出力
     */
    private void logExecutionSummary(int totalJobs, int successCount, int failureCount) {
        logger.info("=== 計画・実績取得バッチ実行結果（S3インポート版） ===");
        logger.info("業務名: 計画・実績取得バッチ（S3インポート版）");
        logger.info("総ジョブ数: {}", totalJobs);
        logger.info("成功: {}", successCount);
        logger.info("失敗: {}", failureCount);
        logger.info("成功率: {}%", totalJobs > 0 ? (successCount * 100 / totalJobs) : 0);
        logger.info("=====================================");
    }
}

package org.ms.bp.runner;

import org.ms.bp.config.ConfigurationManager;
import org.ms.bp.config.job.S3ExportJobConfig;
import org.ms.bp.config.job.S3ExportJobsConfig;
import org.ms.bp.service.executor.ExportJobExecutor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 事業計画（直接）連携バッチアプリケーション
 * データベースから事業計画データを検索してS3にエクスポートする
 */
public class BusinessPlanExportRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(BusinessPlanExportRunner.class);

    // 設定ファイルパス（固定）
    private static final String CONFIG_FILE_PATH = "business-plan-export.yml";

    // 設定管理器
    private final ConfigurationManager config;
    private final String[] args;

    /**
     * コンストラクタ（起動パラメーター付き）
     */
    public BusinessPlanExportRunner(String[] args) {
        this.args = args;
        // 設定管理器を初期化
        this.config = ConfigurationManager.getInstance();

        // 設定情報をログ出力（デバッグ用）
        config.logConfiguration();

        // MDCにアプリケーション情報を設定（構造化ログ用）
        setupLoggingContext();
    }

    /**
     * ログコンテキストを設定
     */
    private void setupLoggingContext() {
        MDC.put("application", "business-plan-export-batch");
        MDC.put("businessProcess", "事業計画（直接）連携バッチ");
        MDC.put("environment", config.getProperty("app.profile", "dev"));
        MDC.put("version", config.getProperty("app.version", "1.0.0"));

    }

    public int run() {
        try {
            logger.info("=== 事業計画データエクスポート処理開始 ===");

            // 1. 設定値を使用
            logger.info("設定ファイル: {}", CONFIG_FILE_PATH);

            // 2. エクスポートジョブ実行器を初期化
            ExportJobExecutor jobExecutor = new ExportJobExecutor(args);

            // 3. 設定ファイルを読み込み
            S3ExportJobsConfig jobsConfig = config.loadExportJobsConfig(CONFIG_FILE_PATH);
            config.validateConfig(jobsConfig);

            // 4. 全ジョブを実行
            return executeExportJobs(jobExecutor, jobsConfig.getJobs());

        } catch (Exception e) {
            logger.error("事業計画（直接）連携バッチ実行エラー", e);
            return 1;
        }
    }
    
    /**
     * エクスポートジョブを実行
     */
    private int executeExportJobs(ExportJobExecutor jobExecutor, List<S3ExportJobConfig> jobs) {
        logger.info("=== 事業計画データエクスポートジョブ実行開始 (ジョブ数: {}) ===", jobs.size());

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        for (var job : jobs) {
            try {
                logger.info("事業計画データエクスポート開始: {}", job.getTaskId());
                ExportJobExecutor.JobExecutionResult result = jobExecutor.executeJob(job);

                if (result.isSuccess()) {
                    successCount.incrementAndGet();
                    logger.info("事業計画データエクスポート成功: {} (エクスポート件数: {})", 
                               job.getTaskId(), result.getExportedRows());
                } else {
                    failureCount.incrementAndGet();
                    logger.error("事業計画データエクスポート失敗: {}", job.getTaskId());
                }

            } catch (Exception e) {
                logger.error("事業計画データエクスポート中に予期しないエラー: {}", job.getTaskId(), e);
                failureCount.incrementAndGet();
            }
        }

        logExecutionSummary(jobs.size(), successCount.get(), failureCount.get());
        return failureCount.get() > 0 ? 1 : 0;
    }

    /**
     * 実行結果サマリーをログ出力
     */
    private void logExecutionSummary(int totalJobs, int successCount, int failureCount) {
        logger.info("=== 事業計画（直接）連携バッチ実行結果 ===");
        logger.info("業務名: 事業計画（直接）連携バッチ");
        logger.info("総ジョブ数: {}", totalJobs);
        logger.info("成功: {}", successCount);
        logger.info("失敗: {}", failureCount);
        logger.info("成功率: {}%", totalJobs > 0 ? (successCount * 100 / totalJobs) : 0);
        logger.info("========================================");
    }
}

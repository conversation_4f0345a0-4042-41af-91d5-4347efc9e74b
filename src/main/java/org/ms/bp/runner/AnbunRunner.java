package org.ms.bp.runner;

import org.ms.bp.service.integration.db.AuroraConnectionManager;
import org.ms.bp.service.integration.db.JdbcTemplate;
import org.ms.bp.service.integration.model.KakojisekiInfo;
import org.ms.bp.util.DateUtil;
import org.ms.bp.util.MessageCodeList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

public class AnbunRunner {

    private static final Logger logger = LoggerFactory.getLogger(AnbunRunner.class);

    // 設定ファイルパス（固定）
    //private static final String CONFIG_FILE_PATH = "anbun.yml";

    // 設定管理器
    //private final ConfigurationManager config;
    private final String[] args;

    private String targetYear = "";
    private String targetMonth = "";
    private String shoriKbn = "";

    private List<KakojisekiInfo> kakojisekiInfos = new ArrayList<KakojisekiInfo>();
    private List<KakojisekiInfo> anbunInsInfos = new ArrayList<KakojisekiInfo>();

    /**
     * コンストラクタ（起動パラメーター付き）
     */
    public AnbunRunner(String[] args) {
        this.args = args;
        // 設定管理器を初期化
        //this.config = ConfigurationManager.getInstance();

        // 設定情報をログ出力（デバッグ用）
        //config.logConfiguration();

        // MDCにアプリケーション情報を設定（構造化ログ用）
        //setupLoggingContext();
    }

    /**
     * ログコンテキストを設定
     */
    private void setupLoggingContext() {
        //MDC.put("application", "anbun-batch");
        //MDC.put("businessProcess", "按分バッチ");
        //MDC.put("environment", config.getProperty("app.profile", "dev"));
        //MDC.put("version", config.getProperty("app.version", "1.0.0"));

    }

    public int run() {
        try {
            // 開始ログ出力
            logger.info(MessageFormat.format(MessageCodeList.INF_001, "BAT_013"));

            // 各パラメータを取得して保持する
            getParams();

            // パラメータチェック
            if (!checkParams()){
                // 終了ログ出力
                logger.info(MessageFormat.format(MessageCodeList.INF_002, "BAT_008", "Required Parameter Error"));
                return 1;
            }

            // 対象年月を再設定する
            setTargetDate();

            // SK商品PLの取得対象の年月の決定
            //String targetYYYYMM = getTargetYYYYMM(targetYear, targetMonth);

            // SK商品PLサマリと次年度計画マスタを結合とグループコードチェック
            //String sysUnyoKigoCd = getSystmUnyoKigyoCode();

            // 月ごとに按分処理を行う
            String[] monthList = targetMonth.split(",");
            for(String currentMonth : monthList){
                switch(shoriKbn){
                    case "0":
                        // SK商品PLサマリと次年度計画マスタの結合処理
                        // 按分対象の採算管理単位の合計比率を算出する
                        getKakojisekiInfo();

                        // 合計比率からの按分処理
                        // 採算管理単位コードの金額反映

                        break;
                    case "1":
                        // SK商品PLサマリと次年度計画マスタの結合処理
                        // 按分対象の採算管理単位の合計比率を算出する
                        getKakojisekiInfo();

                        break;
                    case "2":
                        // 合計比率からの按分処理
                        // 採算管理単位コードの金額反映
                        break;
                }
            }

            return 0;

        } catch (Exception e) {
            logger.error("案分バッチ実行エラー", e);
            return 1;
        }
    }

    /**
     * SK商品PLサマリと次年度計画マスタの結合処理
     */
    private void getKakojisekiInfo() throws SQLException {

        // SK商品PLサマリと次年度計画マスタの結合SQL作成
        String kakojisekiInfoSql = createKakojisekiInfoSql();
        /*
        String groupCdMakerCd_1_1_Sql = createGroupCdMakerCdSql("1","1");
        String groupCdMakerCd_1_2_Sql = createGroupCdMakerCdSql("1","2");
        String groupCdMakerCd_1_3_Sql = createGroupCdMakerCdSql("1","3");
        String groupCdMakerCd_1_4_Sql = createGroupCdMakerCdSql("1","4");
        String groupCdMakerCd_2_1_Sql = createGroupCdMakerCdSql("2","1");
        String groupCdMakerCd_2_2_Sql = createGroupCdMakerCdSql("2","2");
        String groupCdMakerCd_2_3_Sql = createGroupCdMakerCdSql("2","3");
        String groupCdMakerCd_2_4_Sql = createGroupCdMakerCdSql("2","4");
         */

        // 分母SQL作成
        String bunboSql = createBunboSql(kakojisekiInfoSql);
        // 分子SQL作成
        String bunsiSql = createBunsiSql(kakojisekiInfoSql);

        // 実際実行のSQL作成
        String exeSql = createGokeiHiritsuSql(bunboSql, bunsiSql);

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            kakojisekiInfos = jdbcTemplate.query(
                    exeSql,
                    new Object[]{},
                    rs -> new KakojisekiInfo(
                            rs.getString("nendo"),
                            rs.getString("tuki"),
                            rs.getString("group_code"),
                            rs.getString("maker_code"),
                            rs.getString("ssnkn_tncd"),
                            rs.getString("systm_unyo_kigyo_code"),
                            rs.getString("tky_area_code"),
                            rs.getString("zaichoku_kubun"),
                            rs.getString("mishu_kubun"),
                            rs.getFloat("gokeiHiritsu"),
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null
                    )
            );

        } catch (SQLException e) {
            logger.error("PostgreSQL SK商品PLサマリと次年度計画マスタテーブル取得エラー: {}", e.getMessage(), e);
            throw new SQLException("PostgreSQL SK商品PLサマリと次年度計画マスタテーブル取得エラー: " + e.getMessage(), e);
        }

        // 合計比率の補正（100％にならない場合、最大の比率に差分を足して計算する）、かつ登録情報として保持する
        setGokeiHiritsuAndInsInfo();
    }

    /*
    * 過去実績情報取得SQL作成
    */
    private String createKakojisekiInfoSql(){
        String sql = "";
        sql += "        select ";
        sql += "            t1.tuki, ";                                     // 月
        sql += "            t2.tky_group_code, ";                           // 適用グループコード
        sql += "            t2.tky_area_code, ";                            // 適用エリアコード
        sql += "            t2.group_code, ";                               // グループコード
        sql += "            t2.ssnkn_tncd  jiseki_ssnkn_tncd, ";            // 採算管理単位コード
        sql += "            t1.maker_code, ";                               // メーカーコード
        sql += "            t1.kanri_kk_toki_jssk_urgdk, ";                 // 管理会計当期実績＿総売上高
        sql += "            t1.kanri_kk_toki_jssk_zk_so_urd, ";             // 管理会計当期実績＿在庫総売上高（返品含まない）
        sql += "            t1.kanri_kk_toki_jssk_ck_so_urd, ";             // 管理会計当期実績＿直送総売上高（返品含まない）
        sql += "            t3.systm_unyo_kigyo_code, ";                    // システム運用企業コード
        sql += "            t4.ssnkn_tncd ";                                // 採算管理単位コード
        sql += "        from ";
        sql += "            t_sk_shhn_pl_smmry  t1 ";                       // SK商品PLサマリ
        sql += "        left join  ";
        sql += "            t_jinendo_kkk  t2 ";                            // 次年度計画マスタ
        sql += "        on ";
        sql += "            t1.ssnkn_tncd = t2.ssnkn_tncd ";                // 採算管理単位コード
        sql += "        and  ";
        sql += "            t1.group_code = t2.group_code ";                // グループコード
        sql += "        left join  ";
        sql += "            m_groupmst  t3 ";                               // グループマスタ
        sql += "        on ";
        sql += "            t3.group_code = t2.tky_group_code ";            // 適用グループコード
        sql += "        left join  ";
        sql += "            t_area_mtsh_kkk_ssnkn_tn_c  t4 ";               // エリア＿見通し・計画_採算管理単位C別
        sql += "        on ";
        sql += "            t4.ssnkn_tncd = t2.ssnkn_tncd ";                // 採算管理単位コード
        sql += "        and  ";
        sql += "            (t4.group_code = t2.tky_group_code  or ";       // グループコード = ①適用グループコード
        sql += "             t4.group_code = t2.group_code) ";              // グループコード = ①グループコード
        sql += "        and  ";
        sql += "            t4.nendo = '" + targetYear + "'";               // 年度 = 処理中の対象年度
        sql += "        and  ";
        sql += "            t4.kkk_urg_kei > 0 ";                           // 計画＿総売上高計 > 0

        sql += "        where ";
        sql += "            t1.tuki = '" + targetMonth + "'"; // 月
        sql += "        and  ";
        sql += "            t2.nendo = '" + DateUtil.getNendo() + "'"; // 年度
        return sql;
    }

    /*
     * 分母のデータ作成SQL
     * */
    private String createBunboSql(String kakojisekiInfoSql){

        String bunboSql = " select ";
        bunboSql += "          ttm.nendo, ";                                // 年度
        bunboSql += "          ttm.tuki, ";                                 // 月
        bunboSql += "          ttm.group_code, ";                           // グループコード
        bunboSql += "          ttm.maker_code, ";                           // メーカーコード
        //bunboSql += "          ttm.ssnkn_tncd, ";                           // 採算管理単位コード
        bunboSql += "          ttm.systm_unyo_kigyo_code, ";                // システム運用企業コード
        bunboSql += "          ttm.tky_area_code, ";                        // 適用エリアコード
        bunboSql += "          ttm.zaichoku_kubun, ";                       // 在直区分
        bunboSql += "          ttm.mishu_kubun, ";                          // 未収区分
        bunboSql += "          sum(case when ttm.kanri_kk_toki_jssk_zk_so_urd < 0 then 0 else ttm.kanri_kk_toki_jssk_zk_so_urd)  kanri_kk_toki_jssk_zk_so_urd, ";
        bunboSql += "          sum(case when ttm.kanri_kk_toki_jssk_urgdk < 0 then 0 else ttm.kanri_kk_toki_jssk_urgdk)  kanri_kk_toki_jssk_urgdk ";
        bunboSql += "       from ";
        bunboSql += "           (" + kakojisekiInfoSql + ") ttm ";
        bunboSql += "       left join ";
        bunboSql += "           v_knsts_rieki_kkk_maker tts ";
        bunboSql += "       on ";
        bunboSql += "          ttm.tuki = tts.tuki ";
        bunboSql += "       and ";
        bunboSql += "          ttm.group_code = tts.group_code ";
        bunboSql += "       and ";
        bunboSql += "          ttm.maker_code = tts.maker_code ";
        bunboSql += "       group by ";
        bunboSql += "          ttm.nendo, ";
        bunboSql += "          ttm.tuki, ";
        bunboSql += "          ttm.group_code, ";
        bunboSql += "          ttm.maker_code, ";
        //bunboSql += "          ttm.ssnkn_tncd, ";
        bunboSql += "          ttm.systm_unyo_kigyo_code, ";
        bunboSql += "          ttm.tky_area_code, ";
        bunboSql += "          ttm.zaichoku_kubun, ";
        bunboSql += "          ttm.mishu_kubun, ";

        return bunboSql;
    }

    /*
    * 分子のデータ作成SQL
    * */
    private String createBunsiSql(String kakojisekiInfoSql){

        String bunsiSql = " select ";
        bunsiSql += "          ttm.nendo, ";                                // 年度
        bunsiSql += "          ttm.tuki, ";                                 // 月
        bunsiSql += "          ttm.group_code, ";                           // グループコード
        bunsiSql += "          ttm.maker_code, ";                           // メーカーコード
        bunsiSql += "          ttm.ssnkn_tncd, ";                           // 採算管理単位コード
        bunsiSql += "          ttm.jiseki_ssnkn_tncd, ";                    // 実際採算管理単位コード
        bunsiSql += "          ttm.systm_unyo_kigyo_code, ";                // システム運用企業コード
        bunsiSql += "          ttm.tky_area_code, ";                        // 適用エリアコード
        bunsiSql += "          ttm.zaichoku_kubun, ";                       // 在直区分
        bunsiSql += "          ttm.mishu_kubun, ";                          // 未収区分
        bunsiSql += "          sum(case when ttm.kanri_kk_toki_jssk_zk_so_urd < 0 then 0 else ttm.kanri_kk_toki_jssk_zk_so_urd)  kanri_kk_toki_jssk_zk_so_urd, ";
        bunsiSql += "          sum(case when ttm.kanri_kk_toki_jssk_urgdk < 0 then 0 else ttm.kanri_kk_toki_jssk_urgdk)  kanri_kk_toki_jssk_urgdk ";
        bunsiSql += "       from ";
        bunsiSql += "           (" + kakojisekiInfoSql + ") ttm ";
        bunsiSql += "       left join ";
        bunsiSql += "           v_knsts_rieki_kkk_maker tts ";
        bunsiSql += "       on ";
        bunsiSql += "          ttm.tuki = tts.tuki ";
        bunsiSql += "       and ";
        bunsiSql += "          ttm.group_code = tts.group_code ";
        bunsiSql += "       and ";
        bunsiSql += "          ttm.maker_code = tts.maker_code ";
        bunsiSql += "       group by ";
        bunsiSql += "          ttm.nendo, ";
        bunsiSql += "          ttm.tuki, ";
        bunsiSql += "          ttm.group_code, ";
        bunsiSql += "          ttm.maker_code, ";
        bunsiSql += "          ttm.ssnkn_tncd, ";
        bunsiSql += "          ttm.jiseki_ssnkn_tncd, ";
        bunsiSql += "          ttm.systm_unyo_kigyo_code, ";
        bunsiSql += "          ttm.tky_area_code, ";
        bunsiSql += "          ttm.zaichoku_kubun, ";
        bunsiSql += "          ttm.mishu_kubun, ";

        return bunsiSql;
    }

    /*
    * 合計比率算出
    * */
    private String createGokeiHiritsuSql(String bunboSql, String bunsiSql){
        String gokeiHiritsuSql = "";
        gokeiHiritsuSql += " select ";
        gokeiHiritsuSql += "     tbs.nendo, ";
        gokeiHiritsuSql += "     tbs.tuki, ";
        gokeiHiritsuSql += "     tbs.group_code, ";
        gokeiHiritsuSql += "     tbs.maker_code, ";
        gokeiHiritsuSql += "     tbs.ssnkn_tncd, ";
        gokeiHiritsuSql += "     tbs.systm_unyo_kigyo_code, ";
        gokeiHiritsuSql += "     tbs.tky_area_code, ";
        gokeiHiritsuSql += "     tbs.zaichoku_kubun, ";
        gokeiHiritsuSql += "     tbs.mishu_kubun, ";
        gokeiHiritsuSql += "     case when tbs.kanri_kk_toki_jssk_zk_so_urd > 0 and  tbb.kanri_kk_toki_jssk_zk_so_urd > 0 ";
        gokeiHiritsuSql += "          then round(tbs.kanri_kk_toki_jssk_zk_so_urd / tbb.kanri_kk_toki_jssk_zk_so_urd, 2) ";
        gokeiHiritsuSql += "          when tbs.kanri_kk_toki_jssk_zk_so_urd = 0 and  tbb.kanri_kk_toki_jssk_zk_so_urd = 0 ";
        gokeiHiritsuSql += "               and tbs.kanri_kk_toki_jssk_urgdk > 0 and  tbb.kanri_kk_toki_jssk_urgdk > 0 ";
        gokeiHiritsuSql += "          then round(tbs.kanri_kk_toki_jssk_urgdk / tbb.kanri_kk_toki_jssk_urgdk, 2) ";
        gokeiHiritsuSql += "          else 0 ";
        gokeiHiritsuSql += "          end gokeiHiritsu ";
        gokeiHiritsuSql += " from ";
        gokeiHiritsuSql += "       (" + bunsiSql + ") tbs ";
        gokeiHiritsuSql += " left join ";
        gokeiHiritsuSql += "       (" + bunboSql + ") tbb ";
        gokeiHiritsuSql += " on ";
        gokeiHiritsuSql += "     tbs.group_code = tbb.group_code ";
        gokeiHiritsuSql += " and ";
        gokeiHiritsuSql += "     tbs.maker_code = tbb.maker_code ";
        gokeiHiritsuSql += " order by ";
        gokeiHiritsuSql += "     tbs.group_code, ";
        gokeiHiritsuSql += "     tbs.maker_code, ";
        gokeiHiritsuSql += "     tbs.gokeiHiritsu ";

        return gokeiHiritsuSql;
    }

    /*
    *
    * */
    private void setGokeiHiritsuAndInsInfo(){
        // ループ中のグループコード
        String tmpGroupCd = "";
        // ループ中のメーカーコード
        String tmpMakerCd = "";
        // 按分登録用の情報
        KakojisekiInfo anbunInsInfo = null;
        // 合計比率の加算結果


        // 合計比率の補正（100％にならない場合、最大の比率に差分を足して計算する）、かつ登録情報として保持する
        for(KakojisekiInfo kakojisekiInfo : kakojisekiInfos){

            // グループコードとメーカーコードがいずれか変わった場合、新しい行として作成する
            if(!tmpGroupCd.equals(kakojisekiInfo.getGroup_code()) ||
                    !tmpMakerCd.equals(kakojisekiInfo.getMaker_code())){
                anbunInsInfo = new KakojisekiInfo();
            }

            tmpGroupCd = kakojisekiInfo.getGroup_code();
            tmpMakerCd = kakojisekiInfo.getMaker_code();
        }

    }

    /*
    * 間接利益計画＿メーカー別をあらかじめグループ・メーカーで一意にする
    * 間接利益計画＿メーカー別.在直区分 = '1:在庫、3:直送'
    * 間接利益計画＿メーカー別.未収区分 = '1：企画、2：特別、3：年契、4：その他直接'
    */
    private String createGroupCdMakerCdSql(String zaichoku_kubun, String mishu_kubun){
        String sql = "";
        sql += "        select distinct ";
        sql += "            maker_code, ";
        sql += "            group_code ";
        sql += "        from ";
        sql += "            t_knsts_rieki_kkk_maker ";
        sql += "        where ";
        sql += "            zaichoku_kubun = '" + zaichoku_kubun + "'";
        sql += "        and ";
        sql += "            mishu_kubun = '" + mishu_kubun + "'";
        sql += "        and ";
        sql += "        switch(" + targetMonth + ")";
        sql += "        { ";
        sql += "            case '4': ";
        sql += "                knsts_rieki_kkk_1_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '5': ";
        sql += "                knsts_rieki_kkk_2_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '6': ";
        sql += "                knsts_rieki_kkk_3_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '7': ";
        sql += "                knsts_rieki_kkk_4_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '8': ";
        sql += "                knsts_rieki_kkk_5_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '9': ";
        sql += "                knsts_rieki_kkk_6_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '10': ";
        sql += "                knsts_rieki_kkk_7_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '11': ";
        sql += "                knsts_rieki_kkk_8_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '12': ";
        sql += "                knsts_rieki_kkk_9_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '1': ";
        sql += "                knsts_rieki_kkk_10_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '2': ";
        sql += "                knsts_rieki_kkk_11_tskm <> 0 ";
        sql += "                break; ";
        sql += "            case '3': ";
        sql += "                knsts_rieki_kkk_12_tskm <> 0 ";
        sql += "                break; ";
        sql += "        } ";
        return sql;
    }

    /*
    * 対象年月を再設定する処理
    */
    private void setTargetDate(){
        if(targetYear.isEmpty())
        {
            // 共通_年度取得
            targetYear = DateUtil.getNendo();
        }

        if(targetMonth.isEmpty())
        {
            // ４～３月を対象月度とする
            targetMonth = "4,5,6,7,8,9,10,11,12,1,2,3";
        }
    }

    /*
    * パラメータを取得して保持する
     */
    private void getParams(){
        for (String arg : args) {
            if (arg != null && arg.startsWith("NENDO=")) {
                targetYear = arg.substring("NENDO=".length());
            }
            if (arg != null && arg.startsWith("GETUDO=")) {
                targetMonth = arg.substring("GETUDO=".length());
            }
            if (arg != null && arg.startsWith("SHORIKBN=")) {
                shoriKbn = arg.substring("SHORIKBN=".length());
            }
        }
    }

    /**
     * パラメータチェック処理
     */
    private boolean checkParams(){

        // パラメータチェック
        // 桁数チェック（対象年度）
        if(!targetYear.isEmpty() && targetYear.length() != 4)
        {
            logger.error(MessageFormat.format(MessageCodeList.ERR_020, "年度", 4, 4));
            return false;
        }
        // 桁数チェック（対象月度）
        if(!targetMonth.isEmpty() && targetMonth.length() != 1 && targetMonth.length() != 2)
        {
            logger.error(MessageFormat.format(MessageCodeList.ERR_020, "月度", 1, 2));
            return false;
        }
        // 桁数チェック（処理区分）
        if(shoriKbn.length() != 1)
        {
            logger.error(MessageFormat.format(MessageCodeList.ERR_020, "処理区分", 1, 1));
            return false;
        }

        // 書式チェック（対象年度）
        try {
            if(!targetYear.isEmpty()){
                Integer.parseInt(targetYear);
            }
        }catch (Exception e)
        {
            logger.error(MessageFormat.format(MessageCodeList.ERR_021, "年度", "半角数字"));
            return false;
        }

        // 書式チェック（対象月度）
        try {
            if(!targetMonth.isEmpty()){
                Integer.parseInt(targetMonth);
            }
        }catch (Exception e)
        {
            logger.error(MessageFormat.format(MessageCodeList.ERR_021, "月度", "半角数字"));
            return false;
        }

        // 書式チェック（処理区分）
        try {
            Integer.parseInt(shoriKbn);
        }catch (Exception e)
        {
            logger.error(MessageFormat.format(MessageCodeList.ERR_021, "処理区分", "半角数字"));
            return false;
        }

        return true;
    }

    /**
     * 対象年月取得処理
     */
    private String getTargetYYYYMM(String strYYYY, String strMM) throws SQLException {

        String targetdYYYYMM = "";

        while(targetdYYYYMM.isEmpty())
        {
            strYYYY = Integer.toString((Integer.parseInt(strYYYY) -1));

            // ①で作成した取得対象の年月のSK商品PLサマリテーブルが存在するか確認する
            if(hasTskShhnPlSmmry(strYYYY + strMM))
            {
                targetdYYYYMM = strYYYY + strMM;
            }
        }

        return targetdYYYYMM;
    }

    /**
     * 指定年月のSK商品PLサマリテーブルのデータ存在判定処理
     */
    private boolean hasTskShhnPlSmmry(String strYYYYMM) throws SQLException {
        // SK商品PLサマリテーブルのデータを取得する
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            return jdbcTemplate.exists("t_sk_shhn_pl_smmry", "KSSNZ", strYYYYMM);

        } catch (SQLException e) {
            logger.error("PostgreSQL SK商品PLサマリテーブル取得エラー: {}", e.getMessage(), e);
            throw new SQLException("PostgreSQL SK商品PLサマリテーブル取得エラー: " + e.getMessage(), e);
        }
    }
}

package org.ms.bp.runner;

import org.ms.bp.config.ConfigurationManager;
import org.ms.bp.config.job.S3ImportJobConfig;
import org.ms.bp.config.job.S3ImportJobsConfig;
import org.ms.bp.service.executor.ImportJobExecutor;
import org.ms.bp.service.integration.aws.S3ImportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.ms.bp.util.MessageCodeList;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 共通マスタ取得バッチアプリケーション（S3インポート版）
 * AWS RDS PostgreSQLのS3インポート機能を使用してS3からデータベースに直接データをインポートする
 */
public class CommonMasterImportRunner {

    private static final Logger logger = LoggerFactory.getLogger(CommonMasterImportRunner.class);

    // 設定ファイルパス（固定）
    private static final String CONFIG_FILE_PATH = "common-master-import.yml";

    // 設定管理器
    private final ConfigurationManager config;
    private final String[] args;
    private String tableName;

    /**
     * コンストラクタ（起動パラメーター付き）
     */
    public CommonMasterImportRunner(String[] args) {
        this.args = args;
        // 設定管理器を初期化
        this.config = ConfigurationManager.getInstance();

        // 設定情報をログ出力（デバッグ用）
        config.logConfiguration();

        // MDCにアプリケーション情報を設定（構造化ログ用）
        setupLoggingContext();
    }

    /**
     * ログコンテキストを設定
     */
    private void setupLoggingContext() {
        MDC.put("application", "common-master-import-batch");
        MDC.put("businessProcess", "共通マスタ取得バッチ");
        MDC.put("environment", config.getProperty("app.profile", "dev"));
        MDC.put("version", config.getProperty("app.version", "1.0.0"));

    }

    public int run() {
        try {
            logger.info("=== 共通マスタ取得処理開始（S3インポート版） ===");

            // 1. 設定値を使用
            logger.info("設定ファイル: {}", CONFIG_FILE_PATH);

            // 2. S3インポート設定ファイルを読み込み
            S3ImportJobsConfig jobsConfig = config.loadS3ImportJobsConfig(CONFIG_FILE_PATH);
            config.validateConfig(jobsConfig);

            // 3. AWSリージョンを取得
            String awsRegion = jobsConfig.getAws().getRegion();
            logger.info("AWSリージョン: {}", awsRegion);

            // 4. S3インポートサービスを初期化
            S3ImportService s3ImportService = new S3ImportService(awsRegion);
            ImportJobExecutor jobExecutor = new ImportJobExecutor(s3ImportService, args);

            // 5. 全ジョブを実行
            return executeS3ImportJobs(jobExecutor, jobsConfig.getJobs());

        } catch (Exception e) {
            logger.error("共通マスタ取得バッチ実行エラー", e);
            return 1;
        }
    }
    
    /**
     * S3インポートジョブを実行
     */
    private int executeS3ImportJobs(ImportJobExecutor jobExecutor, List<S3ImportJobConfig> jobs) {
        logger.info("=== 共通マスタS3インポートジョブ実行開始 (ジョブ数: {}) ===", jobs.size());

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        // パラメータのテーブル名取得
        tableName = "";
        for (String arg : args) {
            if (arg != null && arg.startsWith("tableName=")) {
                tableName = arg.substring("tableName=".length());
            }
        }

        for (var job : jobs) {
            if(!tableName.isEmpty() && !job.getTarget().getTableName().equals(tableName))
            {
                continue;
            }
            try {
                logger.info("マスタデータS3インポート開始: {}", job.getTaskId());
                ImportJobExecutor.JobExecutionResult result = jobExecutor.executeJob(job);

                if (result.isSuccess()) {
                    successCount.incrementAndGet();
                    logger.info("マスタデータS3インポート成功: {} (インポート件数: {})",
                               job.getTaskId(), result.getImportedRows());
                } else {
                    failureCount.incrementAndGet();
                    logger.error("マスタデータS3インポート失敗: {} - {}", job.getTaskId(), result.getErrorMessage());
                }

            } catch (Exception e) {
                logger.error("マスタデータS3インポート中に予期しないエラー: {}", job.getTaskId(), e);
                failureCount.incrementAndGet();
            }
        }

        logExecutionSummary(jobs.size(), successCount.get(), failureCount.get());
        return failureCount.get() > 0 ? 1 : 0;
    }

    /**
     * 実行結果サマリーをログ出力
     */
    private void logExecutionSummary(int totalJobs, int successCount, int failureCount) {
        logger.info("=== 共通マスタ取得バッチ実行結果（S3インポート版） ===");
        logger.info("業務名: 共通マスタ取得バッチ（S3インポート版）");
        logger.info("総ジョブ数: {}", totalJobs);
        logger.info("成功: {}", successCount);
        logger.info("失敗: {}", failureCount);
        logger.info("成功率: {}%", totalJobs > 0 ? (successCount * 100 / totalJobs) : 0);
        logger.info("=====================================");
    }
    

}

package org.ms.bp.service.executor;

import org.ms.bp.config.job.S3ImportJobConfig;
import org.ms.bp.service.integration.aws.S3ImportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * S3インポートジョブ実行器
 * AWS RDS PostgreSQLのS3インポート機能を使用してデータインポートを実行
 */
public class ImportJobExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ImportJobExecutor.class);
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final S3ImportService s3ImportService;
    private final String[] args;

    public ImportJobExecutor(S3ImportService s3ImportService, String[] args) {
        this.s3ImportService = s3ImportService;
        this.args = args;
    }

    /**
     * 単一のS3インポートジョブを実行
     *
     * @param jobConfig ジョブ設定
     * @return 実行結果
     */
    public JobExecutionResult executeJob(S3ImportJobConfig jobConfig) {
        String taskId = jobConfig.getTaskId();
        LocalDateTime startTime = LocalDateTime.now();

        logger.info("=== S3インポートジョブ実行開始: {} ({}) ===", taskId, startTime.format(TIMESTAMP_FORMATTER));

        JobExecutionResult result = new JobExecutionResult(taskId, startTime);

        try {
            // S3インポートを実行
            S3ImportService.S3ImportResult importResult = s3ImportService.importFromS3(jobConfig, args);

            // 結果を設定
            result.setSuccess(importResult.success());
            result.setImportedRows(importResult.importedRows());
            result.setErrorMessage(importResult.errorMessage());

            if (importResult.success()) {
                logger.info("S3インポートジョブ実行成功: {} (インポート件数: {})", 
                           taskId, importResult.importedRows());
            } else {
                logger.error("S3インポートジョブ実行失敗: {} - {}", taskId, importResult.errorMessage());
            }

        } catch (SQLException e) {
            result.setSuccess(false);
            result.setErrorMessage("データベースエラー: " + e.getMessage());
            logger.error("S3インポートジョブ実行中にSQLエラー: {} - {}", taskId, e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("予期しないエラー: " + e.getMessage());
            logger.error("S3インポートジョブ実行中に予期しないエラー: {} - {}", taskId, e.getMessage(), e);
        }

        result.setEndTime(LocalDateTime.now());
        logExecutionResult(result);

        return result;
    }

    /**
     * 実行結果をログ出力
     */
    private void logExecutionResult(JobExecutionResult result) {
        logger.info("=== S3インポートジョブ実行結果: {} ===", result.getTaskId());
        logger.info("開始時刻: {}", result.getStartTime().format(TIMESTAMP_FORMATTER));
        logger.info("終了時刻: {}", result.getEndTime().format(TIMESTAMP_FORMATTER));
        logger.info("実行時間: {} 秒", result.getExecutionTimeSeconds());
        logger.info("実行結果: {}", result.isSuccess() ? "成功" : "失敗");
        
        if (result.isSuccess()) {
            logger.info("インポート件数: {}", result.getImportedRows());
        } else {
            logger.error("エラーメッセージ: {}", result.getErrorMessage());
        }
        
        logger.info("=== S3インポートジョブ実行結果終了 ===");
    }

    /**
     * ジョブ実行結果を保持するクラス
     */
    public static class JobExecutionResult {
        private final String taskId;
        private final LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success;
        private String errorMessage;
        private Long importedRows;

        public JobExecutionResult(String taskId, LocalDateTime startTime) {
            this.taskId = taskId;
            this.startTime = startTime;
        }

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public Long getImportedRows() { return importedRows; }
        public void setImportedRows(Long importedRows) { this.importedRows = importedRows; }

        /**
         * 実行時間を秒で取得
         */
        public long getExecutionTimeSeconds() {
            if (endTime == null) {
                return 0;
            }
            return java.time.Duration.between(startTime, endTime).getSeconds();
        }
    }
}

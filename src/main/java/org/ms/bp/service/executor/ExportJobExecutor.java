package org.ms.bp.service.executor;

import lombok.Data;
import org.ms.bp.config.job.S3ExportJobConfig;
import org.ms.bp.service.PostgreSQLExportService;
import org.ms.bp.util.BatchParameterProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * エクスポートジョブ実行器
 * PostgreSQL直接S3エクスポート機能を使用してデータベースからS3にエクスポートする
 */
public class ExportJobExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ExportJobExecutor.class);

    private final PostgreSQLExportService postgreSQLExportService;
    private final BatchParameterProcessor parameterProcessor;
    private final String[] args;

    public ExportJobExecutor() {
        this(null);
    }

    public ExportJobExecutor(String[] args) {
        this.postgreSQLExportService = new PostgreSQLExportService();
        this.args = args;
        this.parameterProcessor = new BatchParameterProcessor();
    }

    /**
     * エクスポートジョブを実行
     *
     * @param jobConfig エクスポートジョブ設定
     * @return 実行結果
     */
    public JobExecutionResult executeJob(S3ExportJobConfig jobConfig) {
        String taskId = jobConfig.getTaskId();
        logger.info("=== エクスポートジョブ実行開始: {} ===", taskId);

        JobExecutionResult result = new JobExecutionResult(taskId, LocalDateTime.now());

        try {
            // 1. バッチパラメータを処理
            logger.info("ステップ1: バッチパラメータ処理");
            BatchParameterProcessor.BatchParameters batchParams =
                parameterProcessor.processParameters(args);

            // 2. SQLパラメータを設定してデータベースソース設定を増強
            logger.info("ステップ2: SQLパラメータ設定");
            DatabaseSourceConfig enhancedSource = enhanceSourceWithParameters(jobConfig.getSource(), batchParams);

            // 3. PostgreSQL直接S3エクスポートを実行
            logger.info("ステップ3: PostgreSQL直接S3エクスポート実行");
            PostgreSQLExportService.PostgreSQLExportResult exportResult =
                postgreSQLExportService.exportToS3(enhancedSource, jobConfig.getTarget());

            result.setTotalRows((int) exportResult.getRowsUploaded());
            result.setExportedRows((int) exportResult.getRowsUploaded());
            result.setSuccess(true);

            logger.info("PostgreSQL直接エクスポート完了: {} 行, {} ファイル, {} バイト",
                       exportResult.getRowsUploaded(), exportResult.getFilesUploaded(), exportResult.getBytesUploaded());

            logger.info("エクスポートジョブ実行成功: {}", taskId);

        } catch (SQLException e) {
            result.setSuccess(false);
            result.setErrorMessage("データベースエラー: " + e.getMessage());
            logger.error("エクスポートジョブ実行失敗: {} - データベースエラー", taskId, e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("予期しないエラー: " + e.getMessage());
            logger.error("エクスポートジョブ実行失敗: {} - 予期しないエラー", taskId, e);
        } finally {
            result.setEndTime(LocalDateTime.now());
            logExecutionSummary(result);
        }

        return result;
    }





    /**
     * 実行結果をログ出力
     */
    private void logExecutionSummary(JobExecutionResult result) {
        logger.info("=== エクスポートジョブ実行結果: {} ===", result.getTaskId());
        logger.info("実行結果: {}", result.isSuccess() ? "成功" : "失敗");
        logger.info("開始時刻: {}", result.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("終了時刻: {}", result.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("総行数: {}", result.getTotalRows());
        logger.info("エクスポート行数: {}", result.getExportedRows());

        if (!result.isSuccess()) {
            logger.error("エラーメッセージ: {}", result.getErrorMessage());
        }
    }

    /**
     * ジョブ実行結果クラス
     */
    @Data
    public static class JobExecutionResult {
        // Getters and Setters
        private final String taskId;
        private final LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success = false;
        private String errorMessage;
        private int totalRows = 0;
        private int exportedRows = 0;

        public JobExecutionResult(String taskId, LocalDateTime startTime) {
            this.taskId = taskId;
            this.startTime = startTime;
        }
    }
}

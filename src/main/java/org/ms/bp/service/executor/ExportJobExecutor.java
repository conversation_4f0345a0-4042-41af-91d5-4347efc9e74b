package org.ms.bp.service.executor;

import org.ms.bp.config.job.S3ExportJobConfig;
import org.ms.bp.config.source.DatabaseSourceConfig;
import org.ms.bp.service.PostgreSQLExportService;
import org.ms.bp.util.BatchParameterProcessor;
import org.ms.bp.util.ParameterEnhancer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * エクスポートジョブ実行器
 * PostgreSQL直接S3エクスポート機能を使用してデータベースからS3にエクスポートする
 */
public class ExportJobExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ExportJobExecutor.class);

    private final PostgreSQLExportService postgreSQLExportService;
    private final BatchParameterProcessor parameterProcessor;
    private final ParameterEnhancer parameterEnhancer;
    private final String[] args;

    public ExportJobExecutor() {
        this(null);
    }

    public ExportJobExecutor(String[] args) {
        this.postgreSQLExportService = new PostgreSQLExportService();
        this.args = args;
        this.parameterProcessor = new BatchParameterProcessor();
        this.parameterEnhancer = new ParameterEnhancer();
    }

    /**
     * エクスポートジョブを実行
     *
     * @param jobConfig エクスポートジョブ設定
     * @return 実行結果
     */
    public JobExecutionResult executeJob(S3ExportJobConfig jobConfig) {
        String taskId = jobConfig.getTaskId();
        logger.info("=== エクスポートジョブ実行開始: {} ===", taskId);

        JobExecutionResult result = new JobExecutionResult(taskId, LocalDateTime.now());

        try {
            // 1. バッチパラメータを処理
            logger.info("ステップ1: バッチパラメータ処理");
            BatchParameterProcessor.BatchParameters batchParams =
                parameterProcessor.processParameters(args);

            // 2. SQL パラメータを増強
            logger.info("ステップ2: SQLパラメータ増強");
            DatabaseSourceConfig enhancedSource = enhanceSourceConfig(
                jobConfig.getSource(), batchParams);

            // 3. PostgreSQL直接S3エクスポートを実行
            logger.info("ステップ3: PostgreSQL直接S3エクスポート実行");
            PostgreSQLExportService.PostgreSQLExportResult exportResult =
                postgreSQLExportService.exportToS3(enhancedSource, jobConfig.getTarget());

            result.setTotalRows((int) exportResult.getRowsUploaded());
            result.setExportedRows((int) exportResult.getRowsUploaded());
            result.setSuccess(true);

            logger.info("PostgreSQL直接エクスポート完了: {} 行, {} ファイル, {} バイト",
                       exportResult.getRowsUploaded(), exportResult.getFilesUploaded(), exportResult.getBytesUploaded());

            logger.info("エクスポートジョブ実行成功: {}", taskId);

        } catch (SQLException e) {
            result.setSuccess(false);
            result.setErrorMessage("データベースエラー: " + e.getMessage());
            logger.error("エクスポートジョブ実行失敗: {} - データベースエラー", taskId, e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("予期しないエラー: " + e.getMessage());
            logger.error("エクスポートジョブ実行失敗: {} - 予期しないエラー", taskId, e);
        } finally {
            result.setEndTime(LocalDateTime.now());
            logExecutionSummary(result);
        }

        return result;
    }

    /**
     * データベースソース設定を増強
     * 設定ファイルの静的パラメータを実行時パラメータで動的に置き換える
     *
     * @param originalSource 元のデータベースソース設定
     * @param batchParams バッチ実行時パラメータ
     * @return 増強されたデータベースソース設定
     */
    private DatabaseSourceConfig enhanceSourceConfig(
            DatabaseSourceConfig originalSource,
            BatchParameterProcessor.BatchParameters batchParams) {

        logger.info("データベースソース設定の増強開始");

        // 元の設定をコピーして新しい設定オブジェクトを作成
        DatabaseSourceConfig enhancedSource = new DatabaseSourceConfig();
        enhancedSource.setQuery(originalSource.getQuery());
        enhancedSource.setTableName(originalSource.getTableName());
        enhancedSource.setWhereCondition(originalSource.getWhereCondition());
        enhancedSource.setOrderBy(originalSource.getOrderBy());

        // パラメータの増強
        Map<String, Object> originalParams = originalSource.getParameters();
        if (parameterEnhancer.needsEnhancement(originalParams)) {
            Map<String, Object> enhancedParams = parameterEnhancer.enhanceParameters(
                originalParams, batchParams);
            enhancedSource.setParameters(enhancedParams);
            logger.info("パラメータ増強完了: {} → {}", originalParams, enhancedParams);
        } else {
            Map<String, Object> enhancedParams = parameterEnhancer.addSqlParameters(
                    originalParams, batchParams);
            //enhancedSource.setParameters(originalParams);
            enhancedSource.setParameters(enhancedParams);
            logger.info("パラメータ増強不要: {}", originalParams);
        }

        return enhancedSource;
    }



    /**
     * 実行結果をログ出力
     */
    private void logExecutionSummary(JobExecutionResult result) {
        logger.info("=== エクスポートジョブ実行結果: {} ===", result.getTaskId());
        logger.info("実行結果: {}", result.isSuccess() ? "成功" : "失敗");
        logger.info("開始時刻: {}", result.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("終了時刻: {}", result.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("総行数: {}", result.getTotalRows());
        logger.info("エクスポート行数: {}", result.getExportedRows());

        if (!result.isSuccess()) {
            logger.error("エラーメッセージ: {}", result.getErrorMessage());
        }
    }

    /**
     * ジョブ実行結果クラス
     */
    public static class JobExecutionResult {
        private final String taskId;
        private final LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success = false;
        private String errorMessage;
        private int totalRows = 0;
        private int exportedRows = 0;

        public JobExecutionResult(String taskId, LocalDateTime startTime) {
            this.taskId = taskId;
            this.startTime = startTime;
        }

        // Getters and Setters
        public String getTaskId() {
            return taskId;
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public int getTotalRows() {
            return totalRows;
        }

        public void setTotalRows(int totalRows) {
            this.totalRows = totalRows;
        }

        public int getExportedRows() {
            return exportedRows;
        }

        public void setExportedRows(int exportedRows) {
            this.exportedRows = exportedRows;
        }
    }
}

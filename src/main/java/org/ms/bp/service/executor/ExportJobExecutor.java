package org.ms.bp.service.executor;

import lombok.Data;
import org.ms.bp.config.job.S3ExportJobConfig;
import org.ms.bp.config.source.DatabaseSourceConfig;
import org.ms.bp.service.PostgreSQLExportService;
import org.ms.bp.util.BatchParameterProcessor;
import org.ms.bp.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * エクスポートジョブ実行器
 * PostgreSQL直接S3エクスポート機能を使用してデータベースからS3にエクスポートする
 */
public class ExportJobExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ExportJobExecutor.class);

    private final PostgreSQLExportService postgreSQLExportService;
    private final BatchParameterProcessor parameterProcessor;
    private final String[] args;

    public ExportJobExecutor() {
        this(null);
    }

    public ExportJobExecutor(String[] args) {
        this.postgreSQLExportService = new PostgreSQLExportService();
        this.args = args;
        this.parameterProcessor = new BatchParameterProcessor();
    }

    /**
     * エクスポートジョブを実行
     *
     * @param jobConfig エクスポートジョブ設定
     * @return 実行結果
     */
    public JobExecutionResult executeJob(S3ExportJobConfig jobConfig) {
        return executeJob(jobConfig, null);
    }

    /**
     * エクスポートジョブを実行（カスタムパラメータ付き）
     *
     * @param jobConfig エクスポートジョブ設定
     * @param customParameters Runnerクラスで定義されたカスタムパラメータ
     * @return 実行結果
     */
    public JobExecutionResult executeJob(S3ExportJobConfig jobConfig, Map<String, Object> customParameters) {
        String taskId = jobConfig.getTaskId();
        logger.info("=== エクスポートジョブ実行開始: {} ===", taskId);

        JobExecutionResult result = new JobExecutionResult(taskId, LocalDateTime.now());

        try {
            // 1. バッチパラメータを処理
            logger.info("ステップ1: バッチパラメータ処理");
            BatchParameterProcessor.BatchParameters batchParams =
                parameterProcessor.processParameters(args);

            // 2. SQLパラメータを設定してデータベースソース設定を増強
            logger.info("ステップ2: SQLパラメータ設定");
            DatabaseSourceConfig enhancedSource = enhanceSourceWithParameters(jobConfig.getSource(), batchParams, customParameters);

            // 3. PostgreSQL直接S3エクスポートを実行
            logger.info("ステップ3: PostgreSQL直接S3エクスポート実行");
            PostgreSQLExportService.PostgreSQLExportResult exportResult =
                postgreSQLExportService.exportToS3(enhancedSource, jobConfig.getTarget());

            result.setTotalRows((int) exportResult.getRowsUploaded());
            result.setExportedRows((int) exportResult.getRowsUploaded());
            result.setSuccess(true);

            logger.info("PostgreSQL直接エクスポート完了: {} 行, {} ファイル, {} バイト",
                       exportResult.getRowsUploaded(), exportResult.getFilesUploaded(), exportResult.getBytesUploaded());

            logger.info("エクスポートジョブ実行成功: {}", taskId);

        } catch (SQLException e) {
            result.setSuccess(false);
            result.setErrorMessage("データベースエラー: " + e.getMessage());
            logger.error("エクスポートジョブ実行失敗: {} - データベースエラー", taskId, e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("予期しないエラー: " + e.getMessage());
            logger.error("エクスポートジョブ実行失敗: {} - 予期しないエラー", taskId, e);
        } finally {
            result.setEndTime(LocalDateTime.now());
            logExecutionSummary(result);
        }

        return result;
    }





    /**
     * データベースソース設定にSQLパラメータを設定
     * 命令行参数から解析されたパラメータとRunnerクラスのカスタムパラメータをDatabaseSourceConfigに設定する
     *
     * @param originalSource 元のデータベースソース設定
     * @param batchParams バッチ実行時パラメータ
     * @param customParameters Runnerクラスで定義されたカスタムパラメータ
     * @return パラメータが設定されたデータベースソース設定
     */
    private DatabaseSourceConfig enhanceSourceWithParameters(
            DatabaseSourceConfig originalSource,
            BatchParameterProcessor.BatchParameters batchParams,
            Map<String, Object> customParameters) {

        logger.info("データベースソース設定のパラメータ増強開始");

        // 元の設定をコピーして新しい設定オブジェクトを作成
        DatabaseSourceConfig enhancedSource = new DatabaseSourceConfig();
        enhancedSource.setQuery(originalSource.getQuery());
        enhancedSource.setTableName(originalSource.getTableName());
        enhancedSource.setWhereCondition(originalSource.getWhereCondition());
        enhancedSource.setOrderBy(originalSource.getOrderBy());

        // SQLパラメータを構築
        Map<String, Object> parameters = buildSqlParameters(batchParams, customParameters);
        enhancedSource.setParameters(parameters);

        logger.info("パラメータ増強完了: {}", parameters);
        return enhancedSource;
    }

    /**
     * バッチパラメータとカスタムパラメータからSQLパラメータを構築
     *
     * @param batchParams バッチ実行時パラメータ
     * @param customParameters Runnerクラスで定義されたカスタムパラメータ
     * @return SQLパラメータのMap
     */
    private Map<String, Object> buildSqlParameters(BatchParameterProcessor.BatchParameters batchParams, Map<String, Object> customParameters) {
        Map<String, Object> parameters = new HashMap<>();

        // 1. NENDO（年度）パラメータの処理
        String nendo = batchParams.additionalParameters().get("NENDO");
        if (nendo != null && !nendo.trim().isEmpty()) {
            parameters.put("NENDO", nendo.trim());
            logger.info("NENDO パラメータ設定: {}", nendo);
        } else {
            // デフォルト値として現在の年度を使用
            String defaultNendo = DateUtil.getNendo();
            parameters.put("NENDO", defaultNendo);
            logger.info("NENDO パラメータ（デフォルト）: {}", defaultNendo);
        }

        // 2. targetDate（対象日付）パラメータの処理
        if (batchParams.targetDate() != null) {
            parameters.put("targetDate", batchParams.targetDate().toString());
            logger.info("targetDate パラメータ設定: {}", batchParams.targetDate());
        }

        // 3. その他の追加パラメータを処理
        for (Map.Entry<String, String> entry : batchParams.additionalParameters().entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // NENDO は既に処理済みなのでスキップ
            if (!"NENDO".equals(key) && value != null && !value.trim().isEmpty()) {
                parameters.put(key, value.trim());
                logger.debug("追加パラメータ設定: {} = {}", key, value);
            }
        }

        // 4. Runnerクラスで定義されたカスタムパラメータを追加
        if (customParameters != null && !customParameters.isEmpty()) {
            logger.info("カスタムパラメータを追加: {}", customParameters);
            for (Map.Entry<String, Object> entry : customParameters.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (key != null && value != null) {
                    // カスタムパラメータは優先度が高い（既存のパラメータを上書き）
                    parameters.put(key, value);
                    logger.info("カスタムパラメータ設定: {} = {}", key, value);
                }
            }
        }

        logger.info("構築されたSQLパラメータ数: {}", parameters.size());
        logger.debug("最終SQLパラメータ: {}", parameters);
        return parameters;
    }

    /**
     * 実行結果をログ出力
     */
    private void logExecutionSummary(JobExecutionResult result) {
        logger.info("=== エクスポートジョブ実行結果: {} ===", result.getTaskId());
        logger.info("実行結果: {}", result.isSuccess() ? "成功" : "失敗");
        logger.info("開始時刻: {}", result.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("終了時刻: {}", result.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("総行数: {}", result.getTotalRows());
        logger.info("エクスポート行数: {}", result.getExportedRows());

        if (!result.isSuccess()) {
            logger.error("エラーメッセージ: {}", result.getErrorMessage());
        }
    }

    /**
     * ジョブ実行結果クラス
     */
    @Data
    public static class JobExecutionResult {
        // Getters and Setters
        private final String taskId;
        private final LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success = false;
        private String errorMessage;
        private int totalRows = 0;
        private int exportedRows = 0;

        public JobExecutionResult(String taskId, LocalDateTime startTime) {
            this.taskId = taskId;
            this.startTime = startTime;
        }
    }
}

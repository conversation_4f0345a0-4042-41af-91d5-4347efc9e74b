package org.ms.bp.service;

import lombok.Getter;
import org.ms.bp.config.ConfigurationManager;
import org.ms.bp.config.source.DatabaseSourceConfig;
import org.ms.bp.config.source.S3TargetConfig;
import org.ms.bp.service.integration.db.AuroraConnectionManager;
import org.ms.bp.service.integration.db.JdbcTemplate;
import org.ms.bp.service.integration.aws.S3OptionsBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * PostgreSQL aws_s3.query_export_to_s3 関数を使用したS3エクスポートサービス
 * データベースから直接S3にデータをエクスポート
 */
public class PostgreSQLExportService {

    private static final Logger logger = LoggerFactory.getLogger(PostgreSQLExportService.class);

    // AWSリージョン（設定から取得）
    private static final String AWS_REGION = "ap-northeast-1";

    public PostgreSQLExportService() {
    }
    
    /**
     * データベースから直接S3にエクスポート
     *
     * @param sourceConfig データベースソース設定
     * @param targetConfig S3ターゲット設定
     * @return エクスポート結果
     * @throws SQLException データベースエラー
     */
    public PostgreSQLExportResult exportToS3(DatabaseSourceConfig sourceConfig,
                                            S3TargetConfig targetConfig) throws SQLException {
        
        logger.info("PostgreSQL直接S3エクスポート開始");
        logger.info("バケット: {}, パス: {}", ConfigurationManager.getInstance().getProperty("aws.s3.bucket.name"), targetConfig.getFilePath());
        
        // 1. aws_s3拡張の確認
        verifyAwsS3Extension();
        
        // 2. SQLクエリを準備
        String exportQuery = prepareExportQuery(sourceConfig);
        logger.info("エクスポートクエリ: {}", exportQuery);
        
        // 3. PostgreSQL COPYオプションを構築
        String copyOptions = S3OptionsBuilder.buildCopyOptions(targetConfig);
        logger.info("COPYオプション: {}", copyOptions);
        
        // 4. S3 URI SQLを構築
        String s3UriSql = S3OptionsBuilder.buildS3UriSql(
            ConfigurationManager.getInstance().getProperty("aws.s3.bucket.name"),
            targetConfig.getFilePath(), 
            AWS_REGION
        );
        
        // 5. aws_s3.query_export_to_s3関数を実行
        return executeExportFunction(exportQuery, s3UriSql, copyOptions);
    }
    
    /**
     * aws_s3拡張が利用可能かを確認
     * 
     * @throws SQLException 拡張が利用できない場合
     */
    private void verifyAwsS3Extension() throws SQLException {
        logger.debug("aws_s3拡張の確認開始");
        
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {
            
            String checkSql = "SELECT 1 FROM pg_extension WHERE extname = 'aws_s3'";
            Long result = jdbcTemplate.queryForLong(checkSql, new Object[]{});
            
            if (result == null || result == 0) {
                throw new SQLException("aws_s3拡張がインストールされていません。" +
                    "データベース管理者に連絡してCREATE EXTENSION aws_s3 CASCADE;を実行してください。");
            }
            
            logger.info("aws_s3拡張の確認完了");
        }
    }
    
    /**
     * エクスポート用SQLクエリを準備
     * パラメータ化クエリを完全なSQLに変換し、共通の業務条件を追加
     *
     * @param sourceConfig データベースソース設定
     * @return 実行可能なSQLクエリ
     */
    private String prepareExportQuery(DatabaseSourceConfig sourceConfig) {
        logger.info("エクスポートクエリ準備開始");

        String baseQuery = sourceConfig.getQuery();
        Map<String, Object> parameters = sourceConfig.getParameters();

        // 1. 基本パラメータを置換
        String queryWithParams = replaceQueryParameters(baseQuery, parameters);

        logger.info("エクスポートクエリ準備完了");
        logger.debug("最終クエリ: {}", queryWithParams);

        return queryWithParams;
    }



    /**
     * クエリ内の?プレースホルダーをパラメータで置換
     *
     * @param query クエリ文字列
     * @param parameters パラメータマップ
     * @return 置換後のクエリ
     */
    private String replaceQueryParameters(String query, Map<String, Object> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return query;
        }

        String preparedQuery = query;

        // パラメータを順序通りに置換（?プレースホルダー用）
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String paramName = entry.getKey();
            Object paramValue = entry.getValue();

            // ?プレースホルダーを実際の値に置換
            if (preparedQuery.contains("?")) {
                String safeValue = getSafeParameterValue(paramValue);
                preparedQuery = preparedQuery.replaceFirst("\\?", safeValue);
                logger.debug("パラメータ置換: {} -> {}", paramName, safeValue);
            }
        }

        return preparedQuery;
    }

    /**
     * SQL文字列をエスケープ
     *
     * @param value エスケープする文字列
     * @return エスケープされた文字列
     */
    private String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        // シングルクォートをエスケープ
        return value.replace("'", "''");
    }

    /**
     * パラメータ値を安全な形式に変換
     *
     * @param paramValue パラメータ値
     * @return 安全な値
     */
    private String getSafeParameterValue(Object paramValue) {
        if (paramValue == null) {
            return "NULL";
        }

        if (paramValue instanceof String) {
            return "'" + escapeSqlString(paramValue.toString()) + "'";
        } else if (paramValue instanceof Number) {
            return paramValue.toString();
        } else if (paramValue instanceof Boolean) {
            return paramValue.toString();
        } else {
            return "'" + escapeSqlString(paramValue.toString()) + "'";
        }
    }
    
    /**
     * aws_s3.query_export_to_s3関数を実行
     * 
     * @param exportQuery エクスポートするSQLクエリ
     * @param s3UriSql S3 URI作成SQL
     * @param copyOptions COPYオプション
     * @return エクスポート結果
     * @throws SQLException 実行エラー
     */
    private PostgreSQLExportResult executeExportFunction(String exportQuery, String s3UriSql, String copyOptions) throws SQLException {
        logger.info("aws_s3.query_export_to_s3関数実行開始");
        
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {
            
            // aws_s3.query_export_to_s3関数を呼び出すSQL
            String exportSql = String.format(
                //"SELECT * FROM aws_s3.query_export_to_s3($1, %s, $2)",
                "SELECT * FROM aws_s3.query_export_to_s3(?, %s, ?)",
                s3UriSql
            );
            
            logger.debug("実行SQL: {}", exportSql);
            logger.debug("パラメータ1: {}", exportQuery);
            logger.debug("パラメータ2: {}", copyOptions);
            
            // 関数を実行して結果を取得
            List<PostgreSQLExportResult> results = jdbcTemplate.query(
                exportSql,
                new Object[]{exportQuery, copyOptions},
                rs -> new PostgreSQLExportResult(
                    rs.getLong("rows_uploaded"),
                    rs.getLong("files_uploaded"),
                    rs.getLong("bytes_uploaded")
                )
            );
            
            if (results.isEmpty()) {
                throw new SQLException("aws_s3.query_export_to_s3関数の実行結果が取得できませんでした");
            }
            
            PostgreSQLExportResult result = results.getFirst();
            logger.info("PostgreSQL直接S3エクスポート完了: {} 行, {} ファイル, {} バイト", 
                       result.getRowsUploaded(), result.getFilesUploaded(), result.getBytesUploaded());
            
            return result;
            
        } catch (SQLException e) {
            logger.error("PostgreSQL S3エクスポートエラー: {}", e.getMessage(), e);
            throw new SQLException("PostgreSQL S3エクスポートに失敗しました: " + e.getMessage(), e);
        }
    }

    /**
     * PostgreSQL S3エクスポート結果クラス
     */
    @Getter
    public static class PostgreSQLExportResult {
        private final long rowsUploaded;
        private final long filesUploaded;
        private final long bytesUploaded;

        public PostgreSQLExportResult(long rowsUploaded, long filesUploaded, long bytesUploaded) {
            this.rowsUploaded = rowsUploaded;
            this.filesUploaded = filesUploaded;
            this.bytesUploaded = bytesUploaded;
        }

    }
}

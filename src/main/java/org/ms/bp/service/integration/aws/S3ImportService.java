package org.ms.bp.service.integration.aws;

import org.ms.bp.config.ConfigurationManager;
import org.ms.bp.config.job.S3ImportJobConfig;
import org.ms.bp.service.PostgreSQLExportService;
import org.ms.bp.service.integration.db.AuroraConnectionManager;
import org.ms.bp.service.integration.db.JdbcTemplate;
import org.ms.bp.service.integration.model.KenmuMaster;
import org.ms.bp.util.BatchParameterProcessor;
import org.ms.bp.util.S3PathResolver;
import org.ms.bp.util.MessageCodeList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.S3Object;

import static org.ms.bp.util.DateUtil.getNendo;


/**
 * AWS RDS PostgreSQL S3インポートサービス
 * aws_s3.table_import_from_s3関数を使用してS3からデータベースに直接データをインポート
 */
public class S3ImportService {

    private static final Logger logger = LoggerFactory.getLogger(S3ImportService.class);

    private final BatchParameterProcessor parameterProcessor;
    private final S3PathResolver pathResolver;
    private final String awsRegion;

    public S3ImportService(String awsRegion) {
        this.awsRegion = awsRegion;
        this.parameterProcessor = new BatchParameterProcessor();
        this.pathResolver = new S3PathResolver();
        logger.info("S3ImportService初期化完了。AWSリージョン: {}", awsRegion);
    }

    /**
     * S3からテーブルにデータをインポート
     *
     * @param jobConfig インポートジョブ設定
     * @param args 起動パラメーター
     * @return インポート結果
     * @throws SQLException データベースエラー
     */
    public S3ImportResult importFromS3(S3ImportJobConfig jobConfig, String[] args) throws SQLException {
        String taskId = jobConfig.getTaskId();
        logger.info("S3インポート開始: {}", taskId);

        // S3ファイルパスを解決
        String resolvedS3FilePath = resolveS3FilePath(jobConfig, args);
        String bucketName = ConfigurationManager.getInstance().getProperty("aws.s3.bucket.name");
        logger.info("S3ファイルパス: s3://{}/{}", bucketName, resolvedS3FilePath);

        // 実際のS3ファイル名を取得
        String actualS3FilePath = getActualS3FilePath(bucketName, resolvedS3FilePath);
        final String s3FilePath;
        if (actualS3FilePath != null) {
            logger.info("実際のS3ファイルパス: s3://{}/{}", bucketName, actualS3FilePath);
            s3FilePath = actualS3FilePath; // 実際のファイル名を使用
        } else {
            logger.warn("実際のS3ファイル名が取得できませんでした。元のファイル名を使用: {}", resolvedS3FilePath);
            s3FilePath = resolvedS3FilePath; // 元のファイル名を使用
        }
        
        logger.info("最終的に使用するS3ファイルパス: s3://{}/{}", bucketName, s3FilePath);

        // S3ファイル存在チェック
        try {
            if (!s3FileExists(bucketName, s3FilePath)) {
                // ファイルが見つからない場合、ディレクトリ内のファイル一覧を表示
                String directoryPath = extractDirectoryPath(resolvedS3FilePath);
                logger.info("ファイルが見つからないため、ディレクトリ内のファイル一覧を確認します: {}", directoryPath);
                listFilesInDirectory(bucketName, directoryPath);
                
                String errMsg = MessageCodeList.ERR_014.replace("{0}", s3FilePath);
                logger.error("[{}] {}", taskId, errMsg);
                return new S3ImportResult(taskId, false, 0L, errMsg);
            }
        } catch (Exception e) {
            logger.error("[{}] S3ファイル存在チェックでエラーが発生しました: {}", taskId, e.getMessage(), e);
            // ファイル存在チェックでエラーが発生した場合も処理を停止
            return new S3ImportResult(taskId, false, 0L, "S3ファイル存在チェックでエラーが発生しました: " + e.getMessage());
        }

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            return jdbcTemplate.executeInTransaction(template -> {
                try {
                    // 特定のテータを退避する
                    String taihiSql = jobConfig.getTarget().getTaihiSql();
                    List<KenmuMaster> results = new ArrayList<KenmuMaster>();
                    if (taihiSql != null && !taihiSql.isEmpty()) {
                        logger.info("データ退避: {}", taihiSql);
                        // 関数を実行して結果を取得
                        results = template.query(
                                taihiSql,
                                new Object[]{},
                                rs -> new KenmuMaster(
                                        rs.getString("SYSTM_UNYO_KIGYO_CODE"),
                                        rs.getString("SHAIN_CODE"),
                                        rs.getString("UNIT_CODE"),
                                        rs.getString("YKSHK_KUBUN"),
                                        rs.getString("KMM_KUBUN"),
                                        rs.getString("RNK_JOTAI_KUBUN"),
                                        rs.getFloat("VRSN"),
                                        rs.getString("RCRD_TRK_NCHJ"),
                                        rs.getString("RCRD_KSHN_NCHJ"),
                                        rs.getString("DATA_KUBUN")
                                )
                        );
                    }

                    // 1. テーブルをクリア（設定されている場合）
                    if (jobConfig.getTarget().isTruncateBeforeImport()) {
                        logger.info("テーブルクリア実行: {}", jobConfig.getTarget().getTableName());
                        template.truncateTable(jobConfig.getTarget().getTableName());
                    }else{
                        // deleteConditionが設定された場合、delete処理を行う
                        String delSql = jobConfig.getTarget().getDeleteCondition();
                        if(!delSql.isEmpty()){
                            logger.info("テーブルクリア実行: {}", jobConfig.getTarget().getTableName());
                            String[] delrgs = new String[1];
                            delrgs[0] = getNendo();
                            logger.info("実行する削除SQL: {}", delSql);
                            logger.info("削除パラメータ: {}", delrgs[0]);
                            int sucessCount = template.update(delSql, delrgs);
                            logger.info("削除成功件数: {}", sucessCount);
                        }
                    }

                    // 2. S3インポートSQLを構築
                    String importSql = buildS3ImportSql(jobConfig, s3FilePath);
                    logger.info("実行するS3インポートSQL: {}", importSql);

                    // 3. S3インポートを実行
                    String importResult = template.queryForObject(importSql, new Object[]{}, rs -> rs.getString(1));

                    // 退避データを登録する
                    for(KenmuMaster kenmuMaster : results){
                        try{
                            template.update(creatInsKenmuMstSql(), setKenmuParams(kenmuMaster));
                        } catch (Exception e) {
                            logger.info("社員ID（{}）が重複しているので、登録しない", kenmuMaster.getShain_code());
                        }
                    }

                    // インポート結果から行数を抽出
                    Long importedRows = extractRowCountFromResult(importResult);
                    
                    logger.info("S3インポート完了: {} - {} 行インポート", taskId, importedRows);
                    return new S3ImportResult(taskId, true, importedRows, null);

                } catch (SQLException e) {
                    logger.error("S3インポートエラー: {} - {}", taskId, e.getMessage(), e);
                    return new S3ImportResult(taskId, false, 0L, e.getMessage());
                }
            });
        }
    }

    private String creatInsKenmuMstSql(){
        String insKmmstSql = " insert into m_jk_kenmumaster(";
        insKmmstSql += "                      systm_unyo_kigyo_code,";
        insKmmstSql += "                      shain_code,";
        insKmmstSql += "                      unit_code,";
        insKmmstSql += "                      ykshk_kubun,";
        insKmmstSql += "                      kmm_kubun,";
        insKmmstSql += "                      rnk_jotai_kubun,";
        insKmmstSql += "                      vrsn,";
        insKmmstSql += "                      rcrd_trk_nchj,";
        insKmmstSql += "                      rcrd_kshn_nchj,";
        insKmmstSql += "                      data_kubun";
        insKmmstSql += "                      ) values (";
        insKmmstSql += "                      ?,?,?,?,?,?,?,?,?,?)";

        return insKmmstSql;
    }

    private Object[] setKenmuParams(KenmuMaster kenmuMaster){

        return new Object[]{
                kenmuMaster.getSystm_unyo_kigyo_code(),
                kenmuMaster.getShain_code(),
                kenmuMaster.getUnit_code(),
                kenmuMaster.getYkshk_kubun(),
                kenmuMaster.getKmm_kubun(),
                kenmuMaster.getRnk_jotai_kubun(),
                kenmuMaster.getVrsn(),
                kenmuMaster.getRcrd_trk_nchj(),
                kenmuMaster.getRcrd_kshn_nchj(),
                kenmuMaster.getData_kubun()
        };
    }

    /**
     * S3ファイルパスを解決
     */
    private String resolveS3FilePath(S3ImportJobConfig jobConfig, String[] args) {
        var source = jobConfig.getSource();

        // バッチパラメーターを処理
        BatchParameterProcessor.BatchParameters batchParameters = parameterProcessor.processParameters(args);

        // S3ImportSourceをSourceConfigに変換
        org.ms.bp.config.source.SourceConfig sourceConfig = convertToSourceConfig(source);

        // S3パスを解決
        S3PathResolver.ResolvedS3Path resolvedPath = pathResolver.resolveS3Path(sourceConfig, batchParameters);

        return resolvedPath.filePath();
    }

    /**
     * S3ImportSourceをSourceConfigに変換
     */
    private org.ms.bp.config.source.SourceConfig convertToSourceConfig(S3ImportJobConfig.S3ImportSource source) {
        org.ms.bp.config.source.SourceConfig sourceConfig = new org.ms.bp.config.source.SourceConfig();
        sourceConfig.setMasterPhysicalName(source.getMasterPhysicalName());
        sourceConfig.setDefaultFilePath(source.getDefaultFilePath());
        sourceConfig.setSupportsDynamicPath(source.isSupportsDynamicPath());
        return sourceConfig;
    }

    /**
     * S3インポートSQLを構築
     */
    private String buildS3ImportSql(S3ImportJobConfig jobConfig, String s3FilePath) {
        var target = jobConfig.getTarget();
        var options = jobConfig.getImportOptions();

        // 実際のテーブルスキーマに基づいてカラムリストを調整
        String adjustedColumnList = adjustColumnListToTableSchema(target.getTableName(), jobConfig.getColumnList());

        // PostgreSQL COPY オプションを構築
        StringBuilder copyOptions = new StringBuilder();
        copyOptions.append("(format csv");
        
        if (options.isHeader()) {
            copyOptions.append(", header");
        }
        
        if (!",".equals(options.getDelimiter())) {
            copyOptions.append(", delimiter '").append(options.getDelimiter()).append("'");
        }
        
        if (!"UTF-8".equals(options.getEncoding())) {
            copyOptions.append(", encoding '").append(options.getEncoding()).append("'");
        }
        
        copyOptions.append(")");

        // S3インポートSQLを構築
        // AWS公式ドキュメントに基づく正しい構文
        return String.format(
                "SELECT aws_s3.table_import_from_s3('%s', '%s', '%s', aws_commons.create_s3_uri('%s', '%s', '%s'))",
                target.getTableName(),
                adjustedColumnList,
                copyOptions,
                ConfigurationManager.getInstance().getProperty("aws.s3.bucket.name"),
                s3FilePath,
                awsRegion
        );
    }

    /**
     * テーブルスキーマに基づいてカラムリストを調整する
     */
    private String adjustColumnListToTableSchema(String tableName, String originalColumnList) {
        try {
            // 実際のテーブルカラムを取得
            Set<String> actualColumns = getTableColumns(tableName);
            
            // 元のカラムリストをパース
            String[] requestedColumns = originalColumnList.split(",");
            List<String> validColumns = new ArrayList<>();
            
            for (String column : requestedColumns) {
                String trimmedColumn = column.trim();
                if (actualColumns.contains(trimmedColumn.toLowerCase())) {
                    validColumns.add(trimmedColumn);
                } else {
                    logger.warn("テーブル {} にカラム {} が存在しません。スキップします。", tableName, trimmedColumn);
                }
            }
            
            String adjustedList = String.join(", ", validColumns);
            logger.info("テーブル {} のカラムリストを調整しました: {} -> {}", tableName, originalColumnList, adjustedList);
            
            return adjustedList;
            
        } catch (Exception e) {
            logger.warn("テーブルスキーマの取得に失敗しました。元のカラムリストを使用します: {}", e.getMessage());
            return originalColumnList;
        }
    }

    /**
     * 指定されたテーブルのカラム一覧を取得する
     */
    private Set<String> getTableColumns(String tableName) {
        Set<String> columns = new HashSet<>();
        
        try (Connection conn = AuroraConnectionManager.getConnection()) {
            String sql = """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = ?
                AND table_schema = 'public'
                ORDER BY ordinal_position
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, tableName.toLowerCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        columns.add(rs.getString("column_name").toLowerCase());
                    }
                }
            }
            
            logger.info("テーブル {} のカラム一覧: {}", tableName, columns);
            
        } catch (SQLException e) {
            logger.error("テーブル {} のカラム一覧取得に失敗しました: {}", tableName, e.getMessage());
        }
        
        return columns;
    }

    /**
     * インポート結果から行数を抽出する
     */
    private Long extractRowCountFromResult(String result) {
        if (result == null || result.trim().isEmpty()) {
            return 0L;
        }
        try {
            // インポート結果は "X rows imported into relation..." の形式で返される
            // 例: "19 rows imported into relation "TMP_M_CATEGORYMST" from file ..."
            String[] parts = result.split(" rows imported");
            if (parts.length > 0) {
                String rowCountStr = parts[0].trim();
                return Long.parseLong(rowCountStr);
            }
        } catch (NumberFormatException e) {
            logger.warn("インポート結果から行数を抽出できませんでした: {}", result, e);
        }
        return 0L;
    }

    /**
     * 指定したS3バケット・キーのファイルが存在するかチェックする
     * マスタ名_yyyymmdd での検索のみを行い、見つからない場合はfalseを返す
     */
    boolean s3FileExists(String bucketName, String keyPrefix) {
        try (S3Client s3Client = S3Client.builder()
                .region(software.amazon.awssdk.regions.Region.of(awsRegion))
                .build()) {
            
            // ディレクトリパスを抽出（ファイル名部分を除く）
            String directoryPath = extractDirectoryPath(keyPrefix);
            String searchPrefix = extractSearchPrefix(keyPrefix);
            
            logger.info("S3ファイル検索: バケット={}, ディレクトリ={}, 検索プレフィックス={}", 
                       bucketName, directoryPath, searchPrefix);
            
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(directoryPath + searchPrefix) // マスタ名_yyyymmdd でプレフィックス検索
                    .build();

            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);

            boolean found = listResponse.contents().stream()
                    .anyMatch(obj -> obj.key().startsWith(directoryPath + searchPrefix) && 
                                   obj.key().endsWith(".csv"));
            
            if (found) {
                String foundFile = listResponse.contents().stream()
                        .filter(obj -> obj.key().startsWith(directoryPath + searchPrefix) && 
                                      obj.key().endsWith(".csv"))
                        .map(S3Object::key)
                        .findFirst()
                        .orElse("不明");
                logger.info("S3ファイルが見つかりました: {}", foundFile);
            } else {
                logger.warn("S3ファイルが見つかりませんでした: 検索プレフィックス={}", directoryPath + searchPrefix);
            }
            
            return found;
        } catch (Exception e) {
            logger.error("S3ファイル一覧取得中にエラー", e);
            throw e;
        }
    }
    
    /**
     * ファイルパスからディレクトリ部分を抽出
     */
    private String extractDirectoryPath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }
        
        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex >= 0) {
            return filePath.substring(0, lastSlashIndex + 1);
        }
        
        return "";
    }
    
    /**
     * ファイルパスから検索プレフィックス（マスタ名_yyyymmdd）を抽出
     */
    private String extractSearchPrefix(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }
        
        String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
        // ファイル名から拡張子を除去
        if (fileName.endsWith(".csv")) {
            fileName = fileName.substring(0, fileName.length() - 4);
        }
        
        // マスタ名_yyyymmdd の形式で検索プレフィックスを抽出
        // 例: M_CATEGORYMST_20250727.csv -> M_CATEGORYMST_20250727
        return fileName;
    }
    
    /**
     * 実際のS3ファイル名を取得
     * マスタ名_yyyymmdd でプレフィックス検索して、実際に存在するファイル名を返す
     * 見つからない場合はnullを返す（マスタ名のみでの検索は行わない）
     */
    private String getActualS3FilePath(String bucketName, String s3FilePath) {
        try (S3Client s3Client = S3Client.builder()
                .region(software.amazon.awssdk.regions.Region.of(awsRegion))
                .build()) {
            
            String directoryPath = extractDirectoryPath(s3FilePath);
            String searchPrefix = extractSearchPrefix(s3FilePath);
            
            logger.info("実際のS3ファイル名検索開始:");
            logger.info("  元のファイルパス: {}", s3FilePath);
            logger.info("  ディレクトリパス: {}", directoryPath);
            logger.info("  検索プレフィックス: {}", searchPrefix);
            logger.info("  完全検索プレフィックス: {}", directoryPath + searchPrefix);
            
            // マスタ名_yyyymmdd で検索
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(directoryPath + searchPrefix)
                    .build();

            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
            
            logger.info("S3検索結果: {}件のオブジェクトが見つかりました", listResponse.contents().size());
            
            // 見つかったファイルの一覧をログ出力
            listResponse.contents().stream()
                    .filter(obj -> obj.key().startsWith(directoryPath + searchPrefix) && 
                                 obj.key().endsWith(".csv"))
                    .forEach(obj -> logger.info("  見つかったファイル: {}", obj.key()));

            String actualFilePath = listResponse.contents().stream()
                    .filter(obj -> obj.key().startsWith(directoryPath + searchPrefix) && 
                                 obj.key().endsWith(".csv"))
                    .map(S3Object::key)
                    .findFirst()
                    .orElse(null);
            
            if (actualFilePath != null) {
                logger.info("実際のS3ファイル名を取得: {}", actualFilePath);
            } else {
                logger.warn("実際のS3ファイル名が見つかりませんでした");
                logger.warn("検索条件: プレフィックス={}, 拡張子=.csv", directoryPath + searchPrefix);
            }
            
            return actualFilePath;
                    
        } catch (Exception e) {
            logger.warn("実際のS3ファイル名取得中にエラー: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 指定されたディレクトリ内のファイル一覧を表示する
     */
    private void listFilesInDirectory(String bucketName, String directoryPath) {
        try (S3Client s3Client = S3Client.builder()
                .region(software.amazon.awssdk.regions.Region.of(awsRegion))
                .build()) {
            
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(directoryPath)
                    .build();

            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);

            logger.info("ディレクトリ内のファイル一覧: {}", directoryPath);
            listResponse.contents().stream()
                    .filter(obj -> !obj.key().endsWith("/")) // ディレクトリは除外
                    .forEach(obj -> logger.info("  ファイル: {}", obj.key()));
        } catch (Exception e) {
            logger.error("ディレクトリ内のファイル一覧取得中にエラー", e);
        }
    }

    /**
     * S3インポート結果を保持するクラス
     */
    public record S3ImportResult(String taskId, boolean success, Long importedRows, String errorMessage) {
    }
}

package org.ms.bp.service.integration.db;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.SQLExceptionOverride;
import org.ms.bp.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ssm.SsmClient;
import software.amazon.awssdk.services.ssm.model.GetParameterRequest;
import software.amazon.awssdk.services.ssm.model.GetParameterResponse;
import software.amazon.awssdk.services.ssm.model.ParameterNotFoundException;
import software.amazon.jdbc.util.SqlState;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Aurora PostgreSQL接続管理クラス
 * AWS Lambda環境向けに最適化し、Secrets Managerと連携して AWS Advanced JDBC Wrapper を活用
 * JDBC接続を直接提供
 */
public class AuroraConnectionManager {
    private static final Logger logger = LoggerFactory.getLogger(AuroraConnectionManager.class);

    // シングルトンパターンのデータソース参照
    private static final AtomicReference<HikariDataSource> dataSourceRef = new AtomicReference<>();

    // Lambda実行コンテキストの追跡用
    private static final Map<String, Long> invocationStartTimes = new HashMap<>();

    // 安全マージン時間（秒）、Lambdaタイムアウト前に接続をクローズ
    private static final int SAFETY_MARGIN = 10;

    // 設定管理器
    private static final ConfigurationManager config = ConfigurationManager.getInstance();

    // Systems Manager Parameter Store設定
    private static final String PARAMETER_PREFIX = config.getProperty("db.parameter.prefix", "/ms-bp/dev/standard/db");
    private static final boolean USE_PARAMETER_STORE = config.getProperty("db.use.parameter.store", "true").equals("true");

    // AWS リージョン
    private static final String REGION = config.getProperty("aws.region", "ap-northeast-1");

    // SSH隧道設定
    private static final boolean SSH_TUNNEL_ENABLED = config.getProperty("ssh.tunnel.enabled", "false").equals("true");

    private AuroraConnectionManager() {
        // プライベートコンストラクタ
    }

    /**
     * データソースを取得
     * @return HikariDataSourceインスタンス
     */
    public static HikariDataSource getDataSource() {
        if (dataSourceRef.get() == null) {
            synchronized (AuroraConnectionManager.class) {
                if (dataSourceRef.get() == null) {
                    createDataSource();
                }
            }
        }

        return dataSourceRef.get();
    }

    /**
     * データソースを作成
     */
    private static void createDataSource() {
        HikariConfig config = new HikariConfig();

        // SSH隧道が有効な場合は隧道を確立
        if (SSH_TUNNEL_ENABLED) {
            setupSSHTunnel();
        }

        // データベース接続情報を取得（優先順位: Parameter Store > Secrets Manager > 設定ファイル）
        DbCredentials credentials = null;

        // 1. Parameter Storeから取得を試行
        if (USE_PARAMETER_STORE) {
            credentials = getDbCredentialsFromParameterStore();
            if (credentials != null) {
                logger.info("Parameter Storeから接続情報を取得しました");
            }
        }

        // 2. どちらも取得できない場合は設定ファイルにフォールバック
        if (credentials == null) {
            logger.warn("Parameter Storeから接続情報を取得できませんでした。設定ファイルを使用します。");
            credentials = getDbCredentialsFromEnv();
        }

        // SSH隧道が有効な場合は接続先をローカルホストに変更
        if (SSH_TUNNEL_ENABLED && SSHTunnelManager.isTunnelActive()) {
            credentials.setHost("localhost");
            credentials.setPort(String.valueOf(SSHTunnelManager.getLocalPort()));
            logger.info("SSH隧道経由でデータベースに接続します: localhost:{}", SSHTunnelManager.getLocalPort());
        }

        // JDBC URL設定（SSH隧道使用時は標準PostgreSQLドライバを使用）
        String jdbcUrl;
        if ((SSH_TUNNEL_ENABLED && SSHTunnelManager.isTunnelActive()) || !USE_PARAMETER_STORE) {
            // SSH隧道経由の場合は標準PostgreSQLドライバを使用
            jdbcUrl = String.format("jdbc:postgresql://%s:%s/%s",
                    credentials.getHost(), credentials.getPort(), credentials.getDbname());
            config.setDriverClassName("org.postgresql.Driver");
            logger.info("SSH隧道経由でPostgreSQLドライバを使用します");
        } else {
            // 通常の場合はAWS JDBC Wrapperを使用
            jdbcUrl = String.format("jdbc:aws-wrapper:postgresql://%s:%s/%s",
                    credentials.getHost(), credentials.getPort(), credentials.getDbname());
            config.setDriverClassName("software.amazon.jdbc.Driver");
            logger.info("AWS JDBC Wrapperを使用します");
        }

        // HikariCP基本設定
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(credentials.getUsername());
        config.setPassword(credentials.getPassword());

        // Aurora PostgreSQL固有の設定
        config.addDataSourceProperty("serverName", credentials.getHost());
        config.addDataSourceProperty("portNumber", credentials.getPort());
        config.addDataSourceProperty("databaseName", credentials.getDbname());

        // AWS JDBC Wrapper プラグイン設定
        Properties dsProps = new Properties();
        dsProps.setProperty("wrapperPlugins", "failover,initialConnection,efm2");  // Enable failover and enhanced monitoring
        dsProps.setProperty("wrapperDialect", "aurora-pg");  // Aurora PostgreSQL dialect
        dsProps.setProperty("failoverTimeoutMs", "30000");  // 30 seconds failover timeout
        dsProps.setProperty("openConnectionRetryTimeoutMs", "10000");  // 10 seconds retry timeout for initial connection
        config.setDataSourceProperties(dsProps);

        // Lambda環境最適化設定
        config.setMaximumPoolSize(5);       // 1つの接続で十分
        config.setMinimumIdle(0);           // 最小アイドル接続数を0に
        config.setIdleTimeout(60000);       // アイドル接続を60秒後に解放
        config.setConnectionTimeout(5000);  // 接続タイムアウト5秒
        config.setMaxLifetime(590000);      // 接続の最大寿命は10分弱、Auroraのアイドル接続クリーンアップを避ける

        // パフォーマンス最適化設定
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");

        // フェイルオーバー時の例外ハンドリング設定
        config.setExceptionOverrideClassName("org.ms.bp.service.integration.db.AuroraConnectionManager$HikariCPSQLExceptionOverride");

        // 接続有効性チェック
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(3000);  // 接続検証タイムアウト3秒

        HikariDataSource dataSource = new HikariDataSource(config);
        dataSourceRef.set(dataSource);

        logger.info("AWS JDBC Wrapper を使用したデータソースが作成されました");
    }

    /**
     * HikariCPのSQL例外オーバーライドクラス
     * AWS JDBC Wrapperのフェイルオーバー関連の例外を適切に処理
     */
    public static class HikariCPSQLExceptionOverride implements SQLExceptionOverride {
        public Override adjudicate(SQLException sqlException) {
            String sqlState = sqlException.getSQLState();
            if (sqlState != null && (
                    sqlState.equalsIgnoreCase(SqlState.COMMUNICATION_LINK_CHANGED.getState()) ||
                            sqlState.equalsIgnoreCase(SqlState.CONNECTION_FAILURE_DURING_TRANSACTION.getState()))) {
                return Override.DO_NOT_EVICT;
            } else {
                return Override.CONTINUE_EVICT;
            }
        }
    }

    /**
     * Systems Manager Parameter Storeから接続情報を取得
     * @return DBの認証情報
     */
    private static DbCredentials getDbCredentialsFromParameterStore() {
        if (PARAMETER_PREFIX == null || PARAMETER_PREFIX.isEmpty()) {
            logger.debug("Parameter Storeのプレフィックスが設定されていません");
            return null;
        }

        try {
            // Systems Managerクライアントを作成
            SsmClient ssmClient = SsmClient.builder()
                    .region(Region.of(REGION))
                    .build();

            // 各パラメータを取得
            DbCredentials credentials = new DbCredentials();

            // ホスト名を取得
            String host = getParameterValue(ssmClient, PARAMETER_PREFIX + "/host");
            if (host == null) {
                logger.warn("Parameter Store: hostパラメータが見つかりません");
                return null;
            }
            credentials.setHost(host);

            // データベース名を取得
            String dbName = getParameterValue(ssmClient, PARAMETER_PREFIX + "/DataBaseName");
            if (dbName == null) {
                logger.warn("Parameter Store: DataBaseNameパラメータが見つかりません");
                return null;
            }
            credentials.setDbname(dbName);

            // ユーザー名を取得
            String username = getParameterValue(ssmClient, PARAMETER_PREFIX + "/MasterUsername");
            if (username == null) {
                logger.warn("Parameter Store: MasterUsernameパラメータが見つかりません");
                return null;
            }
            credentials.setUsername(username);

            // パスワードを取得（SecureString）
            String password = getParameterValue(ssmClient, PARAMETER_PREFIX + "/MasterUserPassword", true);
            if (password == null) {
                logger.warn("Parameter Store: MasterUserPasswordパラメータが見つかりません");
                return null;
            }
            credentials.setPassword(password);

            // ポート番号はデフォルト値を使用（Parameter Storeに設定されていない場合）
            String port = getParameterValue(ssmClient, PARAMETER_PREFIX + "/port");
            credentials.setPort(port != null ? port : "5432");

            logger.info("Parameter Storeから接続情報を正常に取得しました");
            return credentials;

        } catch (Exception e) {
            logger.error("Parameter Storeからの接続情報取得に失敗しました", e);
            return null;
        }
    }

    /**
     * Parameter Storeから単一パラメータ値を取得
     * @param ssmClient SSMクライアント
     * @param parameterName パラメータ名
     * @return パラメータ値
     */
    private static String getParameterValue(SsmClient ssmClient, String parameterName) {
        return getParameterValue(ssmClient, parameterName, false);
    }

    /**
     * Parameter Storeから単一パラメータ値を取得
     * @param ssmClient SSMクライアント
     * @param parameterName パラメータ名
     * @param withDecryption 復号化するかどうか（SecureStringの場合true）
     * @return パラメータ値
     */
    private static String getParameterValue(SsmClient ssmClient, String parameterName, boolean withDecryption) {
        try {
            GetParameterRequest request = GetParameterRequest.builder()
                    .name(parameterName)
                    .withDecryption(withDecryption)
                    .build();

            GetParameterResponse response = ssmClient.getParameter(request);
            String value = response.parameter().value();

            logger.debug("Parameter Store: {} を取得しました", parameterName);
            return value;

        } catch (ParameterNotFoundException e) {
            logger.debug("Parameter Store: {} が見つかりません", parameterName);
            return null;
        } catch (Exception e) {
            logger.error("Parameter Store: {} の取得中にエラーが発生しました", parameterName, e);
            return null;
        }
    }


    /**
     * SSH隧道を設定
     */
    private static void setupSSHTunnel() {
        try {
            // SSH隧道設定を初期化
            Properties tunnelConfig = new Properties();
            tunnelConfig.setProperty("ssh.tunnel.ssh.host", config.getProperty("ssh.tunnel.ssh.host", ""));
            tunnelConfig.setProperty("ssh.tunnel.ssh.port", config.getProperty("ssh.tunnel.ssh.port", "22"));
            tunnelConfig.setProperty("ssh.tunnel.ssh.username", config.getProperty("ssh.tunnel.ssh.username", ""));
            tunnelConfig.setProperty("ssh.tunnel.ssh.private.key.path", config.getProperty("ssh.tunnel.ssh.private.key.path", ""));
            tunnelConfig.setProperty("ssh.tunnel.local.port", config.getProperty("ssh.tunnel.local.port", "15432"));
            tunnelConfig.setProperty("ssh.tunnel.remote.host", config.getProperty("ssh.tunnel.remote.host", ""));
            tunnelConfig.setProperty("ssh.tunnel.remote.port", config.getProperty("ssh.tunnel.remote.port", "5432"));
            tunnelConfig.setProperty("ssh.tunnel.connection.timeout", config.getProperty("ssh.tunnel.connection.timeout", "30000"));
            tunnelConfig.setProperty("ssh.tunnel.keep.alive.interval", config.getProperty("ssh.tunnel.keep.alive.interval", "60000"));

            SSHTunnelManager.initializeConfig(tunnelConfig);

            // SSH隧道を確立
            if (!SSHTunnelManager.establishTunnel()) {
                logger.error("SSH隧道の確立に失敗しました。通常の接続にフォールバックします。");
            }
        } catch (Exception e) {
            logger.error("SSH隧道の設定中にエラーが発生しました", e);
        }
    }

    /**
     * 設定ファイルから接続情報を取得
     * @return DBの認証情報
     */
    private static DbCredentials getDbCredentialsFromEnv() {
        DbCredentials credentials = new DbCredentials();
        credentials.setHost(config.getProperty("db.host", "localhost"));
        credentials.setPort(config.getProperty("db.port", "5432"));
        credentials.setDbname(config.getProperty("db.name", "postgres"));
        credentials.setUsername(config.getProperty("db.username", "postgres"));
        credentials.setPassword(config.getProperty("db.password", "123456"));
        return credentials;
    }

    /**
     * データソースを閉じる
     */
    public static void closeDataSource() {
        HikariDataSource dataSource = dataSourceRef.getAndSet(null);
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("データソースが閉じられました");
        }

        // SSH隧道も閉じる
        if (SSH_TUNNEL_ENABLED) {
            SSHTunnelManager.closeTunnel();
        }
    }

    /**
     * JDBC接続を取得
     * @return Connectionインスタンス
     */
    public static Connection getConnection() throws SQLException {
        return getDataSource().getConnection();
    }


    /**
     * DBの認証情報を保持するクラス
     */
    private static class DbCredentials {
        private String host;
        private String port;
        private String dbname;
        private String username;
        private String password;

        // Getters
        public String getHost() { return host; }
        public String getPort() { return port; }
        public String getDbname() { return dbname; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }

        // Setters
        public void setHost(String host) { this.host = host; }
        public void setPort(String port) { this.port = port; }
        public void setDbname(String dbname) { this.dbname = dbname; }
        public void setUsername(String username) { this.username = username; }
        public void setPassword(String password) { this.password = password; }
    }
}
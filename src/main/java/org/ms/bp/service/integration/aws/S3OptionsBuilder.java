package org.ms.bp.service.integration.aws;

import org.ms.bp.config.source.S3TargetConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * PostgreSQL COPY オプション構築ユーティリティ
 * S3TargetConfigをPostgreSQLのaws_s3.query_export_to_s3関数用のオプション文字列に変換
 */
public class S3OptionsBuilder {
    
    private static final Logger logger = LoggerFactory.getLogger(S3OptionsBuilder.class);
    
    /**
     * S3TargetConfigからPostgreSQL COPYオプション文字列を構築
     * 
     * @param targetConfig S3ターゲット設定
     * @return PostgreSQL COPYオプション文字列
     */
    public static String buildCopyOptions(S3TargetConfig targetConfig) {
        logger.debug("PostgreSQL COPYオプション構築開始: {}", targetConfig);
        
        List<String> options = new ArrayList<>();
        
        // CSV形式を指定
        options.add("format csv");
        
        // ヘッダー設定
        if (targetConfig.isIncludeHeader()) {
            options.add("header true");
        }
        
        // 区切り文字設定
        String delimiter = targetConfig.getDelimiter();
        if (delimiter != null && !delimiter.equals(",")) {
            // デフォルトのカンマ以外の場合のみ指定
            options.add("delimiter '" + escapeDelimiter(delimiter) + "'");
        }
        
        // エンコーディング設定
        String encoding = targetConfig.getEncoding();
        if (encoding != null && !encoding.equalsIgnoreCase("UTF-8")) {
            // UTF-8以外の場合のみ指定（PostgreSQLのデフォルトはUTF8）
            String pgEncoding = convertToPgEncoding(encoding);
            options.add("encoding '" + pgEncoding + "'");
        }
        
        String optionsString = String.join(", ", options);
        logger.debug("PostgreSQL COPYオプション構築完了: {}", optionsString);
        
        return optionsString;
    }
    
    /**
     * 区切り文字をエスケープ
     * 
     * @param delimiter 区切り文字
     * @return エスケープされた区切り文字
     */
    private static String escapeDelimiter(String delimiter) {
        // タブ文字の場合
        if ("\t".equals(delimiter)) {
            return "\\t";
        }
        
        // シングルクォートをエスケープ
        return delimiter.replace("'", "''");
    }
    
    /**
     * エンコーディング名をPostgreSQL形式に変換
     * 
     * @param javaEncoding Javaエンコーディング名
     * @return PostgreSQLエンコーディング名
     */
    private static String convertToPgEncoding(String javaEncoding) {
        return switch (javaEncoding.toUpperCase()) {
            case "UTF-8", "UTF8" -> "UTF8";
            case "SHIFT_JIS", "SJIS" -> "SJIS";
            case "EUC-JP", "EUCJP" -> "EUC_JP";
            case "ISO-8859-1", "ISO8859-1" -> "LATIN1";
            case "WINDOWS-1252" -> "WIN1252";
            default -> {
                logger.warn("未知のエンコーディング: {}、UTF8を使用します", javaEncoding);
                yield "UTF8";
            }
        };
    }
    
    /**
     * S3 URIを構築するためのSQL文を生成
     * 
     * @param bucketName S3バケット名
     * @param filePath S3ファイルパス
     * @param region AWSリージョン
     * @return aws_commons.create_s3_uri関数呼び出しSQL
     */
    public static String buildS3UriSql(String bucketName, String filePath, String region) {
        logger.debug("S3 URI SQL構築: bucket={}, path={}, region={}", bucketName, filePath, region);
        
        return String.format("aws_commons.create_s3_uri('%s', '%s', '%s')",
                escapeSqlString(bucketName),
                escapeSqlString(filePath),
                escapeSqlString(region));
    }
    
    /**
     * SQL文字列をエスケープ
     * 
     * @param value エスケープする文字列
     * @return エスケープされた文字列
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        // シングルクォートをエスケープ
        return value.replace("'", "''");
    }
}

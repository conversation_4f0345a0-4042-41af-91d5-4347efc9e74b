package org.ms.bp.service.integration.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KakojisekiInfo {
    private String nendo;
    private String tuki;
    private String group_code;
    private String maker_code;
    private String ssnkn_tncd;
    private String systm_unyo_kigyo_code;
    private String tky_area_code;
    private String zaichoku_kubun;
    private String mishu_kubun;
    private Float gokeiHiritsu;

    // 登録項目用
    // 企画_在庫_総売上＿比率
    private Float kkk_zaiko_srg_hrts;
    // 企画_直送_総売上＿比率
    private Float kkk_chks_srg_hrts;
    // 特別_在庫_総売上＿比率
    private Float tkbts_zaiko_srg_hrts;
    // 特別_直送_総売上＿比率
    private Float tkbts_chks_srg_hrts;
    // 年契_在庫_総売上＿比率
    private Float nnk_zaiko_srg_hrts;
    // 年契_直送_総売上＿比率
    private Float nnk_chks_srg_hrts;
    // その他直接_在庫_総売上＿比率
    private Float sntchkst_zaiko_srg_hrts;
    // その他直接_直送_総売上＿比率
    private Float sntchkst_chks_srg_hrts;

}

package org.ms.bp;

import org.ms.bp.runner.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * バッチ処理アプリケーションのメインクラス
 * 起動パラメーターからjobIdを取得し、対応するアプリケーションを呼び出す統一エントリーポイント
 * BusinessPlanExportApplicationまたはCommonMasterImportApplicationを呼び出す
 */
public class BatchApplication {

    private static final Logger logger = LoggerFactory.getLogger(BatchApplication.class);

    private final String jobId;
    private final String[] args;

    /**
     * コンストラクタ
     */
    public BatchApplication(String[] args) {
        this.args = args;
        // 起動パラメーターからjobIdを取得
        this.jobId = getJobIdFromArgs(args);

        // MDCにjobId情報を設定
        setupLoggingContext();
    }

    /**
     * ログコンテキストを設定
     */
    private void setupLoggingContext() {

        // 起動パラメーターからJob IDを取得
        String jobId = getJobIdFromArgs(args);
        if (jobId != null) {
            MDC.put("batchJobId", jobId);
        }
    }

    public static void main(String[] args) {
        logger.info("=== バッチアプリケーション統一エントリーポイント開始 ===");
        logger.info("起動パラメーター: {}", args != null ? String.join(" ", args) : "なし");

        try {
            BatchApplication app = new BatchApplication(args);
            int exitCode = app.run();

            logger.info("=== バッチアプリケーション統一エントリーポイント終了 (終了コード: {}) ===", exitCode);
            System.exit(exitCode);

        } catch (IllegalArgumentException e) {
            logger.error("起動パラメーター取得エラー: {}", e.getMessage());
            System.exit(2);
        } catch (Exception e) {
            logger.error("アプリケーション実行中に致命的なエラーが発生しました", e);
            System.exit(1);
        }
    }
    
    public int run() {
        try {
            logger.info("=== バッチ実行開始: jobId={} ===", jobId);

            // jobIdに基づいて対応するアプリケーションを呼び出し
            return delegateToSpecificApplication();

        } catch (Exception e) {
            logger.error("バッチ実行エラー: {}", e.getMessage(), e);
            return 1;
        }
    }

    /**
     * 起動パラメーターからjobIdを取得
     */
    private String getJobIdFromArgs(String[] args) {
        String jobId = getJobIdFromArgsInternal(args);

        if (jobId == null || jobId.trim().isEmpty()) {
            throw new IllegalArgumentException("jobId パラメーターが設定されていません");
        }
        logger.info("JOB_ID: {}", jobId);

        return jobId;
    }

    /**
     * 起動パラメーターからJob IDを取得
     * key=value形式のパラメーターを解析
     */
    private String getJobIdFromArgsInternal(String[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (String arg : args) {
            if (arg != null && arg.startsWith("jobId=")) {
                return arg.substring("jobId=".length());
            }
        }

        return null;
    }

    /**
     * 対応するアプリケーションに処理を委譲
     */
    private int delegateToSpecificApplication() {
        logger.info("=== 専用アプリケーションに処理を委譲: jobId={} ===", jobId);

        switch (jobId.toUpperCase().trim()) {
            case "BUSINESS_PLAN_EXPORT":
                logger.info("BusinessPlanExportApplicationを呼び出します");
                BusinessPlanExportRunner exportApp = new BusinessPlanExportRunner(args);
                return exportApp.run();

            case "BUSINESS_PLAN_INDIRECT_EXPORT":
                logger.info("BusinessPlanIndirectExportApplicationを呼び出します");
                String[] indirect_args = new String[0];
                for (String arg : args) {
                    if (arg != null && arg.startsWith("NENDO=")) {
                        indirect_args = new String[1];
                        indirect_args[0] = arg;
                    }
                }
                BusinessPlanIndirectExportRunner indirectExportApp = new BusinessPlanIndirectExportRunner(indirect_args);
                return indirectExportApp.run();

            case "ANBUN":
                logger.info("AnbunApplicationを呼び出します");
                String[] anbun_args = new String[2];
                for (String arg : args) {
                    if (arg != null && arg.startsWith("NENDO=")) {
                        anbun_args[0] = arg;
                    }
                    if (arg != null && arg.startsWith("GETUDO=")) {
                        anbun_args[1] = arg;
                    }
                }
                AnbunRunner anbunApp = new AnbunRunner(anbun_args);
                return anbunApp.run();

            case "COMMON_MASTER_IMPORT":
                logger.info("CommonMasterImportApplicationを呼び出します");
                CommonMasterImportRunner importApp = new CommonMasterImportRunner(args);
                return importApp.run();

            case "GET_PLAN_TRACK_RECORD":
                logger.info("GetPlanTrackRecordApplicationを呼び出します");
                GetPlanTrackRecordRunner getPlanTrackRecordRunner = new GetPlanTrackRecordRunner(args);
                return getPlanTrackRecordRunner.run();
            default:
                logger.error("サポートされていないjobId: {}", jobId);
                return 1;
        }
    }


}

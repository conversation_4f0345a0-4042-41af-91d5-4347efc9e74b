package org.ms.bp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.LocalDate;

/**
 * パラメータ増強器
 * 設定ファイルの静的パラメータを実行時パラメータで動的に置き換える
 */
public class DateUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 年度取得処理
     */
    public static String getNendo(){

        LocalDate now = LocalDate.now();
        Integer nendo = now.getYear();
        if(now.getMonthValue() > 10)
        {
            nendo = nendo + 1;
        }

        logger.info("年度パラメータ取得: NENDO = {}", nendo);
        return String.valueOf(nendo);
    }
}

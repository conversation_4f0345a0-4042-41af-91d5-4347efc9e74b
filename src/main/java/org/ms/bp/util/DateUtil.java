package org.ms.bp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.LocalDate;

/**
 * 日付ユーティリティクラス
 * 日付関連の共通処理を提供する
 */
public class DateUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 年度取得処理
     */
    public static String getNendo(){

        LocalDate now = LocalDate.now();
        Integer nendo = now.getYear();
        if(now.getMonthValue() > 10)
        {
            nendo = nendo + 1;
        }

        logger.info("年度パラメータ取得: NENDO = {}", nendo);
        return String.valueOf(nendo);
    }
}

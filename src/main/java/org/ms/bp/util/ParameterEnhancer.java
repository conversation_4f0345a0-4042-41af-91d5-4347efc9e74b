package org.ms.bp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * パラメータ増強器
 * 設定ファイルの静的パラメータを実行時パラメータで動的に置き換える
 */
public class ParameterEnhancer {
    
    private static final Logger logger = LoggerFactory.getLogger(ParameterEnhancer.class);
    
    /**
     * パラメータを増強する
     * 設定ファイルの静的パラメータを実行時計算されたパラメータで上書きする
     *
     * @param configParameters 設定ファイルのパラメータ
     * @param batchParams バッチ実行時パラメータ
     * @return 増強されたパラメータ
     */
    public Map<String, Object> enhanceParameters(
            Map<String, Object> configParameters,
            BatchParameterProcessor.BatchParameters batchParams) {
        
        logger.info("=== パラメータ増強処理開始 ===");
        
        // 元のパラメータをコピー
        Map<String, Object> enhancedParams = new HashMap<>();
        if (configParameters != null) {
            enhancedParams.putAll(configParameters);
        }
        
        LocalDate targetDate = batchParams.targetDate();
        logger.info("対象日付: {}", targetDate);
        
        // 1. 年度関連パラメータの計算
        String planYear = String.valueOf(targetDate.getYear());
        enhancedParams.put("plan_year", planYear);
        logger.info("計画年度パラメータ設定: plan_year = {}", planYear);
        
        // 2. 月度関連パラメータの計算
        String planMonth = String.format("%02d", targetDate.getMonthValue());
        enhancedParams.put("plan_month", planMonth);
        logger.info("計画月度パラメータ設定: plan_month = {}", planMonth);
        
        // 3. 月初・月末日付の計算
        LocalDate monthStart = targetDate.withDayOfMonth(1);
        LocalDate monthEnd = targetDate.withDayOfMonth(targetDate.lengthOfMonth());
        
        enhancedParams.put("start_date", monthStart.toString());
        enhancedParams.put("end_date", monthEnd.toString());
        logger.info("日付範囲パラメータ設定: start_date = {}, end_date = {}", 
                   monthStart, monthEnd);
        
        // 4. 月範囲パラメータの計算（月度パフォーマンス用）
        enhancedParams.put("start_month", "01");
        enhancedParams.put("end_month", planMonth);
        logger.info("月範囲パラメータ設定: start_month = 01, end_month = {}", planMonth);
        
        // 5. 会計年度の計算（4月開始と仮定）
        int fiscalYear = targetDate.getMonthValue() >= 4 ? 
            targetDate.getYear() : targetDate.getYear() - 1;
        enhancedParams.put("fiscal_year", String.valueOf(fiscalYear));
        logger.info("会計年度パラメータ設定: fiscal_year = {}", fiscalYear);
        
        // 6. 追加パラメータの処理（起動パラメータから取得）
        Map<String, String> additionalParams = batchParams.additionalParameters();
        if (additionalParams != null && !additionalParams.isEmpty()) {
            logger.info("追加パラメータ処理開始");
            
            // 部門コードフィルタ
            if (additionalParams.containsKey("department_code")) {
                String deptCode = additionalParams.get("department_code");
                enhancedParams.put("department_code", deptCode);
                logger.info("部門コードパラメータ設定: department_code = {}", deptCode);
            }
            
            // プロジェクトコードフィルタ
            if (additionalParams.containsKey("project_code")) {
                String projCode = additionalParams.get("project_code");
                enhancedParams.put("project_code", projCode);
                logger.info("プロジェクトコードパラメータ設定: project_code = {}", projCode);
            }
            
            // ステータスフィルタ
            if (additionalParams.containsKey("status")) {
                String status = additionalParams.get("status");
                enhancedParams.put("status", status);
                logger.info("ステータスパラメータ設定: status = {}", status);
            }

            // 年度
            if (additionalParams.containsKey("NENDO")) {
                String nendo = additionalParams.get("NENDO");
                enhancedParams.put("NENDO", nendo);
                logger.info("年度パラメータ設定: NENDO = {}", nendo);
            }
        }
        
        logger.info("=== パラメータ増強処理完了 ===");
        logger.info("最終パラメータ: {}", enhancedParams);
        
        return enhancedParams;
    }
    /**
     * パラメータを設定する
     *
     * @param configParameters 設定ファイルのパラメータ
     * @param batchParams バッチ実行時パラメータ
     * @return SQLに利用するパラメータを設定する
     */
    public Map<String, Object> addSqlParameters(
            Map<String, Object> configParameters,
            BatchParameterProcessor.BatchParameters batchParams) {

        logger.info("=== パラメータ追加処理開始 ===");

        // 元のパラメータをコピー
        Map<String, Object> enhancedParams = new HashMap<>();

        // 追加パラメータの処理（起動パラメータから取得）
        Map<String, String> additionalParams = batchParams.additionalParameters();
        if (additionalParams != null && !additionalParams.isEmpty()) {
            logger.info("パラメータ追加処理開始");
            // 年度
            if (additionalParams.containsKey("NENDO")) {
                String nendo = additionalParams.get("NENDO");
                enhancedParams.put("NENDO", nendo);
                logger.info("年度パラメータ設定: NENDO = {}", nendo);
            }
        }

        logger.info("=== パラメータ追加処理完了 ===");
        logger.info("最終パラメータ: {}", enhancedParams);

        return enhancedParams;
    }
    
    /**
     * パラメータ増強が必要かどうかを判定
     *
     * @param configParameters 設定ファイルのパラメータ
     * @return 増強が必要な場合true
     */
    public boolean needsEnhancement(Map<String, Object> configParameters) {
        // パラメータが存在する場合は増強対象
        return configParameters != null && !configParameters.isEmpty();
    }
}

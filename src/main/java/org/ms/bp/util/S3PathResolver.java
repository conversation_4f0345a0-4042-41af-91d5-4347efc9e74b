package org.ms.bp.util;

import org.ms.bp.config.source.SourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * S3パス解決サービス
 * 動的なS3ファイルパスの生成とファイル名の決定を行う
 */
public class S3PathResolver {
    
    private static final Logger logger = LoggerFactory.getLogger(S3PathResolver.class);
    
    // 日付フォーマット定義
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 解決されたS3パス情報を保持するクラス
     */
    public record ResolvedS3Path(String bucketName, String filePath, String fileName, LocalDate targetDate,
                                 boolean isParameterOverridden) {

    @Override
        public String toString() {
            return "ResolvedS3Path{" +
                    "bucketName='" + bucketName + '\'' +
                    ", filePath='" + filePath + '\'' +
                    ", fileName='" + fileName + '\'' +
                    ", targetDate=" + targetDate +
                    ", isParameterOverridden=" + isParameterOverridden +
                    '}';
        }
    }
    
    /**
     * S3パスを解決
     * 
     * @param sourceConfig ソース設定
     * @param batchParameters バッチパラメーター
     * @return 解決されたS3パス情報
     */
    public ResolvedS3Path resolveS3Path(SourceConfig sourceConfig, BatchParameterProcessor.BatchParameters batchParameters) {
        logger.info("=== S3パス解決処理開始 ===");
        logger.info("ソース設定: {}", sourceConfig);
        logger.info("バッチパラメーター: {}", batchParameters);
        
        // 1. バケット名の決定
        String bucketName = resolveBucketName(sourceConfig, batchParameters);
        
        // 2. ファイルパスの決定
        String filePath = resolveFilePath(sourceConfig, batchParameters);
        
        // 3. ファイル名の抽出
        String fileName = extractFileName(filePath);
        
        // 4. パラメーターオーバーライドの判定
        boolean isParameterOverridden = batchParameters.s3Path() != null || batchParameters.bucketName() != null;
        
        ResolvedS3Path resolvedPath = new ResolvedS3Path(
            bucketName, 
            filePath, 
            fileName, 
            batchParameters.targetDate(),
            isParameterOverridden
        );
        
        logger.info("S3パス解決完了: {}", resolvedPath);
        return resolvedPath;
    }
    
    /**
     * バケット名を解決
     */
    private String resolveBucketName(SourceConfig sourceConfig, BatchParameterProcessor.BatchParameters batchParameters) {
        // 起動パラメーターでバケット名が指定されている場合は優先
        if (batchParameters.bucketName() != null) {
            logger.info("起動パラメーターで指定されたバケット名を使用: {}", batchParameters.bucketName());
            return batchParameters.bucketName();
        }
        
        // 設定ファイルのバケット名を使用
        logger.info("設定ファイルのバケット名を使用: {}", sourceConfig.getBucketName());
        return sourceConfig.getBucketName();
    }
    
    /**
     * ファイルパスを解決
     */
    private String resolveFilePath(SourceConfig sourceConfig, BatchParameterProcessor.BatchParameters batchParameters) {
        // 起動パラメーターでS3パスが指定されている場合は優先
        if (batchParameters.s3Path() != null) {
            logger.info("起動パラメーターで指定されたS3パスを使用: {}", batchParameters.s3Path());
            return batchParameters.s3Path();
        }
        
        // 動的パス生成が有効な場合
        if (sourceConfig.isSupportsDynamicPath()) {
            return generateDynamicFilePath(sourceConfig, batchParameters);
        }

        // 動的パス生成が無効の場合はエラー
        logger.error("動的パス生成が無効で、起動パラメーターでS3パスも指定されていません");
        throw new IllegalStateException("S3パスを決定できません");
    }
    
    /**
     * 動的ファイルパスを生成
     */
    private String generateDynamicFilePath(SourceConfig sourceConfig, BatchParameterProcessor.BatchParameters batchParameters) {
        logger.info("動的ファイルパス生成開始");
        
        String masterPhysicalName = sourceConfig.getMasterPhysicalName();
        String defaultFilePath = sourceConfig.getDefaultFilePath();
        LocalDate targetDate = batchParameters.targetDate();
        
        if (masterPhysicalName == null || masterPhysicalName.trim().isEmpty()) {
            logger.warn("マスタ物理名が設定されていません。デフォルトパスを使用: {}", defaultFilePath);
            return defaultFilePath;
        }
        
        // 対象年月日をフォーマット
        String formattedDate = targetDate.format(DATE_FORMATTER);
        
        // ファイル名を生成: マスタ物理名 + 年月日
        String dynamicFileName = masterPhysicalName + formattedDate + ".csv";
        
        // デフォルトパスのディレクトリ部分を取得
        String directoryPath = extractDirectoryPath(defaultFilePath);
        
        // 完全なファイルパスを生成
        String dynamicFilePath = directoryPath + dynamicFileName;
        
        logger.info("動的ファイルパス生成完了:");
        logger.info("  マスタ物理名: {}", masterPhysicalName);
        logger.info("  対象年月日: {} ({})", targetDate, formattedDate);
        logger.info("  生成されたファイル名: {}", dynamicFileName);
        logger.info("  完全パス: {}", dynamicFilePath);
        
        return dynamicFilePath;
    }
    
    /**
     * ファイルパスからディレクトリ部分を抽出
     */
    private String extractDirectoryPath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }
        
        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex >= 0) {
            return filePath.substring(0, lastSlashIndex + 1);
        }
        
        return "";
    }
    
    /**
     * ファイルパスからファイル名を抽出
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }
        
        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < filePath.length() - 1) {
            return filePath.substring(lastSlashIndex + 1);
        }
        
        return filePath;
    }
    
    /**
     * S3パスの妥当性を検証
     */
    public boolean validateS3Path(ResolvedS3Path resolvedPath) {
        if (resolvedPath == null) {
            logger.error("解決されたS3パスがnullです");
            return false;
        }
        
        if (resolvedPath.bucketName() == null || resolvedPath.bucketName().trim().isEmpty()) {
            logger.error("バケット名が設定されていません");
            return false;
        }
        
        if (resolvedPath.filePath() == null || resolvedPath.filePath().trim().isEmpty()) {
            logger.error("ファイルパスが設定されていません");
            return false;
        }
        
        if (resolvedPath.fileName() == null || resolvedPath.fileName().trim().isEmpty()) {
            logger.error("ファイル名が設定されていません");
            return false;
        }
        
        logger.info("S3パスの妥当性検証成功");
        return true;
    }
}

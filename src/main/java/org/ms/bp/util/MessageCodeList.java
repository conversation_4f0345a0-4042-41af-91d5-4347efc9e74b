package org.ms.bp.util;

public class MessageCodeList {
    public static final String ERR_001 = "テーブル登録に失敗しました。エラー内容：{0}";
    public static final String ERR_002 = "認証に問題が発生しました。システム管理者に問い合わせてご確認ください。";
    public static final String ERR_003 = "セッションの有効期限が切れました。再度アクセスしてください。";
    public static final String ERR_004 = "システムが混み合っています。しばらくしてからお試しください。";
    public static final String ERR_005 = "必須パラメータ「{0}」がありません。";
    public static final String ERR_006 = "{0}は{1}に存在しないデータです。";
    public static final String ERR_007 = "アップロードされたCSVのヘッダー情報に誤りがあります。";
    public static final String ERR_008 = "行：{0}、エラー内容：登録権限のないエリア情報が入力されています。";
    public static final String ERR_009 = "行：{0}、エラー内容：{1}の内容が不正です。";
    public static final String INF_001 = "処理開始。{0}";
    public static final String INF_002 = "処理終了。{0}　ステータス：{1}";
    public static final String ERR_010 = "アップロードされたファイルは{0}の形式ではありません。内容をご確認ください。";
    public static final String ERR_011 = "CSV作成が正しく行われませんでした。ファイルの種類：{0}、エラー内容：{1}";
    public static final String INF_003 = "{0}を作成しました。履歴番号：{1}";
    public static final String ERR_012 = "条件に一致するデータが取得できませんでした。カラム名：{0}、条件：{1}、エラー内容：{2}";
    public static final String ERR_013 = "予期せぬエラーが発生しました。処理名：{0}、エラー内容：{1}";
    public static final String ERR_014 = "一致するファイルがありませんでした。検索対象：{0}";
    public static final String INF_004 = "実行年月：｛0｝登録件数：{1}";
    public static final String ERR_015 = "ファイル種別{0}をアップロードする権限がありません。";
    public static final String ERR_016 = "行：{0}、項目：{1}、エラー内容：必須チェックエラー";
    public static final String ERR_017 = "行：{0}、項目：{1}、エラー内容：桁数エラー。{2}桁から{3}桁の間で入力してください。";
    public static final String ERR_018 = "行：{0}、項目：{1}、エラー内容：書式エラー。{2}で入力してください。";
    public static final String ERR_019 = "パラメータ：{0}、エラー内容：必須チェックエラー";
    public static final String ERR_020 = "パラメータ：{0}、エラー内容：桁数エラー。{1}桁から{2}桁の間で入力してください。";
    public static final String ERR_021 = "パラメータ：{0}、エラー内容：書式エラー。{1}で入力してください。";
    public static final String ERR_022 = "行：{0}、項目：{1}、エラー内容：項目エラー。{2}は存在しないコードです。";
    public static final String INF_005 = "登録件数：{0}";
    public static final String ERR_023 = "条件に一致するデータが取得できませんでした。";
    public static final String ERR_024 = "行：{0}、項目：エラー内容：グループコード{1}が次年度計画マスタで登録されたグループコードと一致しません。";
}

package org.ms.bp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * バッチ起動パラメーター処理サービス
 * main関数のargs配列から対象年月日やS3パスなどを解析・処理する
 */
public class BatchParameterProcessor {

    private static final Logger logger = LoggerFactory.getLogger(BatchParameterProcessor.class);

    // 日付フォーマット定義（YYYYMMDD形式を優先）
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
        DateTimeFormatter.ofPattern("yyyyMMdd"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd")
    };

    // 起動パラメーター名定義（key=value形式）
    private static final String TARGET_DATE_PARAM = "targetDate";
    private static final String S3_PATH_PARAM = "s3-path";
    private static final String BUCKET_NAME_PARAM = "bucket-name";
    private static final String JOB_ID_PARAM = "jobId";

    /**
     * バッチパラメーター情報を保持するクラス
     */
    public record BatchParameters(LocalDate targetDate, String s3Path, String bucketName, String jobId,
                                  Map<String, String> additionalParameters) {
        public BatchParameters(LocalDate targetDate, String s3Path, String bucketName, String jobId, Map<String, String> additionalParameters) {
            this.targetDate = targetDate;
            this.s3Path = s3Path;
            this.bucketName = bucketName;
            this.jobId = jobId;
            this.additionalParameters = additionalParameters != null ? additionalParameters : new HashMap<>();
        }

        public String getAdditionalParameter(String key) {
            return additionalParameters.get(key);
        }

        @Override
        public String toString() {
            return "BatchParameters{" +
                    "targetDate=" + targetDate +
                    ", s3Path='" + s3Path + '\'' +
                    ", bucketName='" + bucketName + '\'' +
                    ", jobId='" + jobId + '\'' +
                    ", additionalParameters=" + additionalParameters +
                    '}';
        }
    }
    
    /**
     * 起動パラメーターを処理してBatchParametersオブジェクトを生成
     *
     * @param args main関数から渡されるコマンドライン引数
     * @return 処理されたバッチパラメーター
     */
    public BatchParameters processParameters(String[] args) {
        logger.info("=== バッチ起動パラメーター処理開始 ===");
        logger.info("コマンドライン引数: {}", args != null ? String.join(" ", args) : "なし");

        // 引数をパース
        Map<String, String> parsedArgs = parseArguments(args);

        // 1. 対象年月日の決定
        LocalDate targetDate = determineTargetDate(parsedArgs);
        logger.info("決定された対象年月日: {}", targetDate);

        // 2. S3パスの決定
        String s3Path = determineS3Path(parsedArgs);
        if (s3Path != null) {
            logger.info("起動パラメーターで指定されたS3パス: {}", s3Path);
        } else {
            logger.info("S3パスは起動パラメーターで指定されていません（デフォルトパスを使用）");
        }

        // 3. バケット名の決定
        String bucketName = determineBucketName(parsedArgs);
        if (bucketName != null) {
            logger.info("起動パラメーターで指定されたバケット名: {}", bucketName);
        } else {
            logger.info("バケット名は起動パラメーターで指定されていません（設定ファイルを使用）");
        }

        // 4. Job IDの取得
        String jobId = determineJobId(parsedArgs);
        if (jobId != null) {
            logger.info("起動パラメーターで指定されたJob ID: {}", jobId);
        } else {
            logger.info("Job IDは起動パラメーターで指定されていません");
        }

        // 5. その他のパラメーターを収集
        Map<String, String> additionalParameters = collectAdditionalParameters(parsedArgs);

        BatchParameters parameters = new BatchParameters(targetDate, s3Path, bucketName, jobId, additionalParameters);
        logger.info("バッチパラメーター処理完了: {}", parameters);

        return parameters;
    }
    
    /**
     * コマンドライン引数をパースしてMapに変換
     * key=value形式のパラメーターを解析
     *
     * @param args コマンドライン引数
     * @return パースされた引数のMap
     */
    private Map<String, String> parseArguments(String[] args) {
        Map<String, String> parsedArgs = new HashMap<>();

        if (args == null || args.length == 0) {
            logger.info("コマンドライン引数がありません");
            return parsedArgs;
        }

        for (String arg : args) {
            if (arg != null && arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                if (parts.length == 2) {
                    String key = parts[0].trim();
                    String value = parts[1].trim();
                    parsedArgs.put(key, value);
                    logger.debug("パラメーター解析: {} = {}", key, value);
                }
            }
        }

        logger.info("解析されたパラメーター数: {}", parsedArgs.size());
        return parsedArgs;
    }

    /**
     * 対象年月日を決定
     * ②-1 起動パラメーターで対象年月日が指定されている場合、起動パラメーターよりファイル取得年月日を決定する。
     * ②-2 起動パラメーターで対象年月日が指定されていない場合、システム年月日の前日よりファイル取得年月日を決定する。
     * UTC時間戳形式（2025-06-27T01:05:49Z）もサポート
     */
    private LocalDate determineTargetDate(Map<String, String> parsedArgs) {
        String targetDateStr = parsedArgs.get(TARGET_DATE_PARAM);

        if (targetDateStr != null && !targetDateStr.trim().isEmpty()) {
            logger.info("起動パラメーターで対象年月日が指定されています: {}", targetDateStr);

            // 指定された日付をパース
            LocalDate parsedDate = parseDate(targetDateStr.trim());
            if (parsedDate != null) {
                return parsedDate;
            } else {
                logger.warn("指定された対象年月日のフォーマットが不正です: {}。システム日付の前日を使用します。", targetDateStr);
            }
        } else {
            logger.info("起動パラメーターで対象年月日が指定されていません。システム日付の前日を使用します。");
        }

        // システム日付の前日を返す
        LocalDate yesterday = LocalDate.now().minusDays(1);
        logger.info("システム日付の前日を使用: {}", yesterday);
        return yesterday;
    }
    
    /**
     * S3パスを決定
     * ②-1起動パラメーターでCSVファイルのS3パスが指定されている場合、起動パラメーターよりCSVファイルのS3パスを決定する。
     * ②-1起動パラメーターでCSVファイルのS3パスが指定されていない場合、既定のファイルパスよりCSVファイルのS3パスを決定する。
     */
    private String determineS3Path(Map<String, String> parsedArgs) {
        String s3Path = parsedArgs.get(S3_PATH_PARAM);
        
        if (s3Path != null && !s3Path.trim().isEmpty()) {
            return s3Path.trim();
        }
        
        return null; // nullの場合はデフォルトパスを使用
    }
    
    /**
     * バケット名を決定
     */
    private String determineBucketName(Map<String, String> parsedArgs) {
        String bucketName = parsedArgs.get(BUCKET_NAME_PARAM);
        
        if (bucketName != null && !bucketName.trim().isEmpty()) {
            return bucketName.trim();
        }
        
        return null; // nullの場合は設定ファイルのバケット名を使用
    }
    
    /**
     * Job IDを決定
     */
    private String determineJobId(Map<String, String> parsedArgs) {
        String jobId = parsedArgs.get(JOB_ID_PARAM);

        if (jobId != null && !jobId.trim().isEmpty()) {
            return jobId.trim();
        }

        return null; // nullの場合は環境変数から取得するか、エラーとする
    }

    /**
     * その他のパラメーターを収集
     */
    private Map<String, String> collectAdditionalParameters(Map<String, String> parsedArgs) {
        Map<String, String> additionalParams = new HashMap<>();

        // 既知のパラメーター以外を追加パラメーターとして収集
        for (Map.Entry<String, String> entry : parsedArgs.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 既知のパラメーター以外を収集
            if (!TARGET_DATE_PARAM.equals(key) &&
                !S3_PATH_PARAM.equals(key) &&
                !BUCKET_NAME_PARAM.equals(key) &&
                !JOB_ID_PARAM.equals(key) &&
                value != null && !value.trim().isEmpty()) {
                additionalParams.put(key, value.trim());
                logger.debug("追加パラメーター収集: {} = {}", key, value);
            }
        }

        return additionalParams;
    }
    
    /**
     * 起動パラメーターから対象日付を取得（Optional形式）
     * S3ImportServiceとの互換性のために追加
     */
    public java.util.Optional<LocalDate> getTargetDateFromArgs(String[] args) {
        Map<String, String> parsedArgs = parseArguments(args);
        String targetDateStr = parsedArgs.get(TARGET_DATE_PARAM);

        if (targetDateStr != null && !targetDateStr.trim().isEmpty()) {
            LocalDate parsedDate = parseDate(targetDateStr.trim());
            return java.util.Optional.ofNullable(parsedDate);
        }

        return java.util.Optional.empty();
    }

    /**
     * 起動パラメーターからS3パスを取得（Optional形式）
     * S3ImportServiceとの互換性のために追加
     */
    public java.util.Optional<String> getS3PathFromArgs(String[] args) {
        Map<String, String> parsedArgs = parseArguments(args);
        String s3Path = parsedArgs.get(S3_PATH_PARAM);

        if (s3Path != null && !s3Path.trim().isEmpty()) {
            return java.util.Optional.of(s3Path.trim());
        }

        return java.util.Optional.empty();
    }

    /**
     * 日付文字列をパース
     * YYYYMMDD、yyyy-MM-dd、yyyy/MM/dd、UTC時間戳形式をサポート
     */
    private LocalDate parseDate(String dateStr) {
        // UTC時間戳形式の場合（例：2025-06-27T01:05:49Z）
        if (dateStr.contains("T") && dateStr.endsWith("Z")) {
            try {
                Instant instant = Instant.parse(dateStr);
                LocalDate date = instant.atOffset(ZoneOffset.UTC).toLocalDate();
                logger.info("UTC時間戳を日付に変換: {} -> {}", dateStr, date);
                return date;
            } catch (DateTimeParseException e) {
                logger.warn("UTC時間戳の解析に失敗: {}", dateStr);
            }
        }

        // 通常の日付フォーマットを試行
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDate.parse(dateStr, formatter);
            } catch (DateTimeParseException e) {
                // 次のフォーマットを試行
            }
        }

        logger.error("日付フォーマットが認識できません: {}", dateStr);
        return null;
    }
}
